(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{6861:function(e,t,a){Promise.resolve().then(a.bind(a,8071))},8071:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Dashboard}});var r=a(7437),s=a(2265),n=a(3549),o=a(4033),l=a(6831),i=a(4086),d=a(5813),c=a(5632),m=a(1393);let SliderInput=e=>{let{label:t,value:a,onChange:s,min:n,max:o,step:l,description:i,icon:d}=e,m=(()=>{let e=(n+o)/2;return a<.8*e?"baixo":a>1.2*e?"alto":"medio"})();return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[d,t]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg",children:a.toFixed(1)}),(0,r.jsx)("div",{className:"text-xs px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 font-medium",children:"baixo"===m?"Baixo":"medio"===m?"Padr\xe3o":"Alto"})]})]}),(0,r.jsxs)("div",{className:"relative py-4",children:[(0,r.jsx)("div",{className:"w-full h-1 bg-white/20 rounded-full relative",children:(0,r.jsx)("div",{className:"absolute left-1/2 top-0 bottom-0 w-0.5 bg-white/40 transform -translate-x-1/2"})}),(0,r.jsx)("input",{type:"range",min:n,max:o,step:l,value:a,onChange:e=>s(parseFloat(e.target.value)),className:"absolute inset-0 w-full opacity-0 cursor-pointer z-10"}),(0,r.jsxs)("div",{className:"absolute -bottom-2 left-0 right-0 flex justify-between text-xs text-white/40",children:[(0,r.jsx)("span",{children:n}),(0,r.jsx)("span",{className:"font-semibold text-blue-300",children:"1.0"}),(0,r.jsx)("span",{children:o})]}),(0,r.jsx)(c.E.div,{className:"absolute top-1/2 w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-lg shadow-blue-500/30 border-2 border-white/20 cursor-pointer",style:{left:"".concat((a-n)/(o-n)*100,"%")},whileHover:{scale:1.2},whileTap:{scale:.9},transition:{duration:.2},children:(0,r.jsx)("div",{className:"absolute inset-1 bg-white/20 rounded-full"})})]}),(0,r.jsx)("p",{className:"text-white/60 text-xs leading-relaxed",children:i})]})};function CreateChatModal(e){let{isOpen:t,onClose:a,username:n,onChatCreated:o,editingChat:u}=e,[h,x]=(0,s.useState)("geral"),[p,g]=(0,s.useState)(!1),[b,f]=(0,s.useState)(""),[v,j]=(0,s.useState)({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048});(0,s.useEffect)(()=>{u&&t?loadChatData():!u&&t&&j({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048})},[u,t]);let loadChatData=async()=>{if(u)try{let e=await (0,i.getDoc)((0,i.doc)(l.db,"usuarios",n,"conversas",u.id));if(e.exists()){let t=e.data();j({name:t.name||"",systemPrompt:t.systemPrompt||"",context:t.context||"",password:t.password||"",latexInstructions:t.latexInstructions||!1,temperature:t.temperature||1,frequencyPenalty:t.frequencyPenalty||1,repetitionPenalty:t.repetitionPenalty||1,maxTokens:t.maxTokens||2048})}}catch(e){console.error("Erro ao carregar dados do chat:",e),f("Erro ao carregar dados do chat")}},handleInputChange=(e,t)=>{j(a=>({...a,[e]:t}))},generateChatId=()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,8);return"chat_".concat(e,"_").concat(t)},createChat=async()=>{if(!v.name.trim()){f("Nome do chat \xe9 obrigat\xf3rio");return}g(!0),f("");try{if(u){let e=new Date().toISOString(),t={context:v.context,frequencyPenalty:v.frequencyPenalty,lastUpdatedAt:e,latexInstructions:v.latexInstructions,maxTokens:v.maxTokens,name:v.name,password:v.password,repetitionPenalty:v.repetitionPenalty,systemPrompt:v.systemPrompt,temperature:v.temperature,updatedAt:e};await (0,i.r7)((0,i.doc)(l.db,"usuarios",n,"conversas",u.id),t),console.log("Chat atualizado com sucesso:",u.id),o(u.id),a()}else{let e=generateChatId(),t=new Date().toISOString(),r={context:v.context,createdAt:t,folderId:null,frequencyPenalty:v.frequencyPenalty,isFixed:!1,lastUpdatedAt:t,lastUsedModel:"",latexInstructions:v.latexInstructions,maxTokens:v.maxTokens,name:v.name,password:v.password,repetitionPenalty:v.repetitionPenalty,sessionTime:{lastSessionStart:null,lastUpdated:null,totalTime:0},systemPrompt:v.systemPrompt,temperature:v.temperature,ultimaMensagem:"",ultimaMensagemEm:null,updatedAt:t};await (0,i.pl)((0,i.doc)(l.db,"usuarios",n,"conversas",e),r);let s={id:e,name:v.name,messages:[],createdAt:t,lastUpdated:t},c=new Blob([JSON.stringify(s,null,2)],{type:"application/json"}),m=(0,d.iH)(l.tO,"usuarios/".concat(n,"/conversas/").concat(e,"/chat.json"));await (0,d.KV)(m,c),console.log("Chat criado com sucesso:",e),o(e),a(),j({name:"",systemPrompt:"",context:"",password:"",latexInstructions:!1,temperature:1,frequencyPenalty:1,repetitionPenalty:1,maxTokens:2048})}}catch(e){console.error("Erro ao processar chat:",e),f(u?"Erro ao atualizar conversa. Tente novamente.":"Erro ao criar conversa. Tente novamente.")}finally{g(!1)}};return t?(0,r.jsx)(m.M,{children:(0,r.jsxs)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:a,children:[(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"}),(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(20)].map((e,t)=>(0,r.jsx)(c.E.div,{className:"absolute w-2 h-2 bg-blue-400/20 rounded-full",initial:{x:Math.random()*window.innerWidth,y:Math.random()*window.innerHeight,scale:0},animate:{y:[null,-100],scale:[0,1,0],opacity:[0,.6,0]},transition:{duration:3*Math.random()+2,repeat:1/0,delay:2*Math.random()}},t))}),(0,r.jsxs)(c.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-0 bottom-0 right-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent"}),(0,r.jsxs)(c.E.div,{className:"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(c.E.div,{className:"relative w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30",whileHover:{scale:1.1,rotate:5},transition:{duration:.3},children:[(0,r.jsx)(c.E.svg,{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",initial:{scale:0},animate:{scale:1},transition:{delay:.4,type:"spring"},children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-white/20 rounded-2xl blur-xl"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.E.h2,{className:"text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.3},children:u?"Editar Conversa":"Nova Conversa"}),(0,r.jsx)(c.E.p,{className:"text-white/70 text-sm mt-1",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},children:u?"Altere as configura\xe7\xf5es da conversa":"Configure sua nova conversa com IA"})]})]}),(0,r.jsx)(c.E.button,{onClick:a,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-3 hover:bg-white/10 rounded-2xl group",whileHover:{scale:1.1,rotate:90},whileTap:{scale:.9},transition:{duration:.2},children:(0,r.jsx)("svg",{className:"w-5 h-5 transition-transform duration-300 group-hover:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)(c.E.div,{className:"flex bg-gradient-to-r from-white/10 to-white/5 mx-6 mt-6 rounded-2xl p-1 backdrop-blur-sm border border-white/10",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,r.jsxs)(c.E.button,{onClick:()=>x("geral"),className:"flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ".concat("geral"===h?"text-white shadow-xl":"text-white/70 hover:text-white hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:["geral"===h&&(0,r.jsx)(c.E.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl",layoutId:"activeTab",transition:{type:"spring",bounce:.2,duration:.6}}),(0,r.jsx)(c.E.svg,{className:"w-5 h-5 relative z-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",animate:{rotate:"geral"===h?360:0},transition:{duration:.5},children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),(0,r.jsx)("span",{className:"font-semibold relative z-10",children:"Geral"})]}),(0,r.jsxs)(c.E.button,{onClick:()=>x("avancado"),className:"flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden ".concat("avancado"===h?"text-white shadow-xl":"text-white/70 hover:text-white hover:bg-white/10"),whileHover:{scale:1.02},whileTap:{scale:.98},children:["avancado"===h&&(0,r.jsx)(c.E.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl",layoutId:"activeTab",transition:{type:"spring",bounce:.2,duration:.6}}),(0,r.jsxs)(c.E.svg,{className:"w-5 h-5 relative z-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",animate:{rotate:"avancado"===h?360:0},transition:{duration:.5},children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,r.jsx)("span",{className:"font-semibold relative z-10",children:"Avan\xe7ado"})]})]}),(0,r.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[60vh]",children:[(0,r.jsxs)(m.M,{mode:"wait",children:["geral"===h&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Nome do chat"]}),(0,r.jsx)("span",{className:"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full",children:"obrigat\xf3rio"})]}),(0,r.jsx)("input",{type:"text",value:v.name,onChange:e=>handleInputChange("name",e.target.value),placeholder:"Ex: Projeto de f\xedsica",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Nome obrigat\xf3rio para identificar a conversa"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"System Prompt"]}),(0,r.jsx)("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),(0,r.jsx)("textarea",{value:v.systemPrompt,onChange:e=>handleInputChange("systemPrompt",e.target.value),placeholder:"Instru\xe7\xf5es para o comportamento da IA...",rows:3,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:'Define como a IA deve se comportar e responder (ex: "Seja um assistente especializado em matem\xe1tica")'})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),"Contexto"]}),(0,r.jsx)("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),(0,r.jsx)("textarea",{value:v.context,onChange:e=>handleInputChange("context",e.target.value),placeholder:"Informa\xe7\xf5es adicionais de contexto para a conversa...",rows:3,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Informa\xe7\xf5es de fundo que a IA deve considerar durante toda a conversa"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",viewBox:"0 0 24 24",children:[(0,r.jsx)("rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2"}),(0,r.jsx)("path",{d:"M7 11V7a5 5 0 0 1 10 0v4"})]}),"Senha do Chat"]}),(0,r.jsx)("span",{className:"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full",children:"opcional"})]}),(0,r.jsx)("input",{type:"password",value:v.password,onChange:e=>handleInputChange("password",e.target.value),placeholder:"Deixe vazio para chat sem prote\xe7\xe3o",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Se definida, ser\xe1 necess\xe1rio inserir a senha para acessar este chat"})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Instru\xe7\xf5es LaTeX"]}),(0,r.jsx)("p",{className:"text-white/60 text-xs mt-1",children:"Habilita formata\xe7\xe3o matem\xe1tica avan\xe7ada"})]}),(0,r.jsx)("button",{onClick:()=>handleInputChange("latexInstructions",!v.latexInstructions),className:"relative w-14 h-7 rounded-full transition-all duration-300 ".concat(v.latexInstructions?"bg-gradient-to-r from-blue-500 to-cyan-500":"bg-white/20"),children:(0,r.jsx)("div",{className:"absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ".concat(v.latexInstructions?"left-8":"left-1")})})]})})]},"geral"),"avancado"===h&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-6",children:[(0,r.jsx)(SliderInput,{label:"Temperatura",value:v.temperature,onChange:e=>handleInputChange("temperature",e),min:0,max:2,step:.1,description:"Controla a criatividade das respostas. Esquerda = mais preciso (0.1-0.8), Centro = balanceado (1.0), Direita = mais criativo (1.2-2.0).",icon:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z",clipRule:"evenodd"})})}),(0,r.jsx)(SliderInput,{label:"Frequency Penalty",value:v.frequencyPenalty,onChange:e=>handleInputChange("frequencyPenalty",e),min:0,max:2,step:.1,description:"Reduz repeti\xe7\xe3o de palavras. Esquerda = sem penalidade (0.0-0.8), Centro = padr\xe3o (1.0), Direita = alta penalidade (1.2-2.0).",icon:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,r.jsx)(SliderInput,{label:"Repetition Penalty",value:v.repetitionPenalty,onChange:e=>handleInputChange("repetitionPenalty",e),min:0,max:2,step:.1,description:"Penaliza tokens repetidos. Esquerda = sem penalidade (0.0-0.8), Centro = padr\xe3o (1.0), Direita = alta penalidade (1.2-2.0).",icon:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"})}),"Limite de Tokens"]}),(0,r.jsx)("span",{className:"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg",children:v.maxTokens})]}),(0,r.jsx)("input",{type:"number",value:v.maxTokens,onChange:e=>handleInputChange("maxTokens",parseInt(e.target.value)||2048),min:512,max:8192,step:256,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),(0,r.jsx)("p",{className:"text-white/60 text-xs leading-relaxed",children:"M\xe1ximo de tokens que a IA pode gerar por resposta. Valores t\xedpicos: 512-4096."})]})]},"avancado")]}),b&&(0,r.jsx)(c.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm",children:b})]}),(0,r.jsx)(c.E.div,{className:"p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-transparent backdrop-blur-sm",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(c.E.button,{onClick:a,disabled:p,className:"px-8 py-4 text-white/70 hover:text-white transition-all duration-500 hover:bg-white/10 rounded-2xl disabled:opacity-50 font-semibold border border-white/20 hover:border-white/30 backdrop-blur-sm",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},transition:{duration:.2},children:"Cancelar"}),(0,r.jsxs)(c.E.button,{onClick:createChat,disabled:p||!v.name.trim(),className:"px-10 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-2xl transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 font-semibold shadow-xl shadow-blue-500/30 hover:shadow-blue-500/50 border border-blue-400/30 hover:border-blue-400/50 backdrop-blur-sm relative overflow-hidden",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},transition:{duration:.2},children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"}),p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.E.div,{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),(0,r.jsx)("span",{children:u?"Salvando...":"Criando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.E.svg,{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",whileHover:{rotate:u?0:90},transition:{duration:.3},children:u?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{children:u?"Salvar Altera\xe7\xf5es":"Criar Conversa"})]})]})]})})]})]})}):null}let u=[{name:"Azul",value:"blue",bg:"bg-blue-500",selected:"bg-blue-600"},{name:"Verde",value:"green",bg:"bg-green-500",selected:"bg-green-600"},{name:"Amarelo",value:"yellow",bg:"bg-yellow-500",selected:"bg-yellow-600"},{name:"Vermelho",value:"red",bg:"bg-red-500",selected:"bg-red-600"},{name:"Roxo",value:"purple",bg:"bg-purple-500",selected:"bg-purple-600"},{name:"Ciano",value:"cyan",bg:"bg-cyan-500",selected:"bg-cyan-600"},{name:"Lima",value:"lime",bg:"bg-lime-500",selected:"bg-lime-600"},{name:"Laranja",value:"orange",bg:"bg-orange-500",selected:"bg-orange-600"},{name:"Rosa",value:"pink",bg:"bg-pink-500",selected:"bg-pink-600"},{name:"Cinza",value:"gray",bg:"bg-gray-500",selected:"bg-gray-600"}];function CreateFolderModal(e){let{isOpen:t,onClose:a,username:n,onFolderCreated:o,editingFolder:d=null}=e,[h,x]=(0,s.useState)(!1),[p,g]=(0,s.useState)(""),[b,f]=(0,s.useState)({name:"",description:"",color:"blue",expandedByDefault:!0});(0,s.useEffect)(()=>{d?f({name:d.name,description:d.description||"",color:d.color,expandedByDefault:d.expandedByDefault}):f({name:"",description:"",color:"blue",expandedByDefault:!0})},[d]);let handleInputChange=(e,t)=>{f(a=>({...a,[e]:t}))},generateFolderId=()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,8);return"folder_".concat(e,"_").concat(t)},createFolder=async()=>{if(!b.name.trim()){g("Nome da pasta \xe9 obrigat\xf3rio");return}x(!0),g("");try{let e=new Date().toISOString();if(d){let t={name:b.name,description:b.description,color:b.color,expandedByDefault:b.expandedByDefault,updatedAt:e};await (0,i.r7)((0,i.doc)(l.db,"usuarios",n,"pastas",d.id),t),console.log("Pasta atualizada com sucesso:",d.id),o(d.id)}else{let t=generateFolderId(),a={name:b.name,description:b.description,color:b.color,expandedByDefault:b.expandedByDefault,createdAt:e,updatedAt:e,chatCount:0};await (0,i.pl)((0,i.doc)(l.db,"usuarios",n,"pastas",t),a),console.log("Pasta criada com sucesso:",t),o(t)}a(),d||f({name:"",description:"",color:"blue",expandedByDefault:!0})}catch(e){console.error("Erro ao salvar pasta:",e),g(d?"Erro ao atualizar pasta. Tente novamente.":"Erro ao criar pasta. Tente novamente.")}finally{x(!1)}};return t?(0,r.jsx)(m.M,{children:(0,r.jsxs)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:a,children:[(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl"}),(0,r.jsxs)(c.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})}),(0,r.jsx)("div",{children:(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:d?"Editar Pasta":"Nova Pasta"})})]}),(0,r.jsx)("button",{onClick:a,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Nome da pasta"]}),(0,r.jsx)("span",{className:"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full",children:"obrigat\xf3rio"})]}),(0,r.jsx)("input",{type:"text",value:b.name,onChange:e=>handleInputChange("name",e.target.value),placeholder:"Digite o nome da pasta",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Nome para identificar a pasta de chats"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Descri\xe7\xe3o"]}),(0,r.jsx)("textarea",{value:b.description,onChange:e=>handleInputChange("description",e.target.value),placeholder:"Descri\xe7\xe3o opcional da pasta",rows:2,className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none"}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Descri\xe7\xe3o opcional para a pasta"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-blue-300",children:"Cor da pasta"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-2",children:u.map(e=>(0,r.jsx)("button",{onClick:()=>handleInputChange("color",e.value),className:"w-10 h-10 rounded-xl transition-all duration-200 ".concat(b.color===e.value?"".concat(e.selected," ring-2 ring-white/50 scale-110"):"".concat(e.bg," hover:scale-105")),title:e.name,children:b.color===e.value&&(0,r.jsx)("svg",{className:"w-5 h-5 text-white mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})},e.value))}),(0,r.jsx)("p",{className:"text-white/60 text-xs",children:"Escolha uma cor para identificar visualmente a pasta"})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-300",children:"Expandida por padr\xe3o"}),(0,r.jsx)("p",{className:"text-white/60 text-xs mt-1",children:"A pasta ficar\xe1 aberta quando voc\xea acessar o chat"})]}),(0,r.jsx)("button",{onClick:()=>handleInputChange("expandedByDefault",!b.expandedByDefault),className:"relative w-14 h-7 rounded-full transition-all duration-300 ".concat(b.expandedByDefault?"bg-gradient-to-r from-blue-500 to-cyan-500":"bg-white/20"),children:(0,r.jsx)("div",{className:"absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 ".concat(b.expandedByDefault?"left-8":"left-1")})})]})}),p&&(0,r.jsx)(c.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm",children:p})]}),(0,r.jsx)("div",{className:"p-6 border-t border-white/10 bg-white/5",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:a,disabled:h,className:"px-6 py-3 text-white/70 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium",children:"Cancelar"}),(0,r.jsx)("button",{onClick:createFolder,disabled:h||!b.name.trim(),className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:d?"Salvando...":"Criando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:d?"M5 13l4 4L19 7":"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{children:d?"Salvar Altera\xe7\xf5es":"Criar Pasta"})]})})]})})]})]})}):null}function ConfirmDeleteModal(e){let{isOpen:t,onClose:a,onConfirm:s,title:n,message:o,itemName:l,isLoading:i=!1}=e;return t?(0,r.jsx)(m.M,{children:(0,r.jsxs)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:a,children:[(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/40 via-red-900/30 to-black/40 backdrop-blur-xl"}),(0,r.jsxs)(c.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/90 via-red-900/90 to-slate-900/90 backdrop-blur-2xl border border-red-500/30 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-red-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-red-500/20 bg-gradient-to-r from-red-500/10 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/30",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("div",{children:(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:n})})]}),(0,r.jsx)("button",{onClick:a,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-white/80 text-base leading-relaxed",children:o}),(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-500/20 border border-red-500/30 rounded-xl",children:(0,r.jsxs)("p",{className:"text-red-300 font-semibold text-sm",children:['"',l,'"']})}),(0,r.jsx)("p",{className:"text-red-400/80 text-sm mt-3 font-medium",children:"Esta a\xe7\xe3o n\xe3o pode ser desfeita."})]})}),(0,r.jsx)("div",{className:"p-6 border-t border-red-500/20 bg-red-500/5",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:a,disabled:i,className:"px-6 py-3 text-white/70 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium",children:"Cancelar"}),(0,r.jsx)("button",{onClick:s,disabled:i,className:"px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium shadow-lg shadow-red-500/25 hover:shadow-red-500/40",children:i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Deletando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),(0,r.jsx)("span",{children:"Deletar"})]})})]})})]})]})}):null}function PasswordProtectedModal(e){let{isOpen:t,onClose:a,onSuccess:n,chatName:o,onPasswordSubmit:l}=e,[i,d]=(0,s.useState)(""),[u,h]=(0,s.useState)(!1),[x,p]=(0,s.useState)(""),[g,b]=(0,s.useState)(!1),handleSubmit=async e=>{if(e.preventDefault(),!i.trim()){p("Digite a senha");return}h(!0),p("");try{let e=await l(i);e?(d(""),n()):(p("Senha incorreta. Tente novamente."),d(""))}catch(e){console.error("Erro ao verificar senha:",e),p("Erro ao verificar senha. Tente novamente.")}finally{h(!1)}},handleClose=()=>{d(""),p(""),b(!1),a()};return t?(0,r.jsx)(m.M,{children:(0,r.jsxs)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4",onClick:handleClose,children:[(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-gradient-to-br from-black/60 via-blue-900/40 to-purple-900/60 backdrop-blur-xl"}),(0,r.jsxs)(c.E.div,{initial:{scale:.8,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:.8,opacity:0,y:50},transition:{type:"spring",duration:.6,bounce:.3},className:"relative bg-gradient-to-br from-slate-900/95 via-blue-900/95 to-indigo-900/95 backdrop-blur-2xl border border-blue-500/30 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-blue-900/50",onClick:e=>e.stopPropagation(),children:[(0,r.jsxs)("div",{className:"relative p-6 border-b border-blue-500/20 bg-gradient-to-r from-blue-500/10 to-transparent",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30 relative",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-2xl blur-lg opacity-30 -z-10"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"Chat Protegido"}),(0,r.jsx)("p",{className:"text-blue-200/80 text-sm",children:"Digite a senha para continuar"})]})]}),(0,r.jsx)("button",{onClick:handleClose,className:"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-2xl flex items-center justify-center mx-auto mb-3 border border-blue-500/30",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-1",children:o}),(0,r.jsx)("p",{className:"text-blue-300/70 text-sm",children:"Esta conversa est\xe1 protegida por senha"})]}),(0,r.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("label",{className:"text-sm font-medium text-blue-300 flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"Senha"]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:g?"text":"password",value:i,onChange:e=>d(e.target.value),placeholder:"Digite a senha da conversa",className:"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 pr-12 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15",disabled:u}),(0,r.jsx)("button",{type:"button",onClick:()=>b(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",disabled:u,children:g?(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):(0,r.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]}),x&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("span",{children:x})]}),(0,r.jsx)("button",{type:"submit",disabled:u||!i.trim(),className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white py-3 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transform",children:u?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Verificando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"})}),(0,r.jsx)("span",{children:"Desbloquear"})]})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-white/50 text-xs",children:"A senha \xe9 necess\xe1ria para acessar esta conversa protegida"})})]})]})]})}):null}let h=(0,s.forwardRef)((e,t)=>{let{userData:n,isOpen:o,isCollapsed:c=!1,onToggle:m,onSettingsOpen:u,onChatSelect:h,currentChat:x,onUpdateOpenRouterBalance:p,showCloseButton:g=!0}=e,[b,f]=(0,s.useState)([]),[v,j]=(0,s.useState)([]),[w,y]=(0,s.useState)({balance:0,isLoading:!0,error:void 0}),[N,k]=(0,s.useState)(!1),[C,M]=(0,s.useState)(!1),[S,E]=(0,s.useState)(null),[L,D]=(0,s.useState)(!1),[A,I]=(0,s.useState)(!1),[T,F]=(0,s.useState)(null),[P,z]=(0,s.useState)(null),[B,R]=(0,s.useState)(null),[O,W]=(0,s.useState)(null),[U,H]=(0,s.useState)({isOpen:!1,type:"chat",id:"",name:""}),[_,V]=(0,s.useState)(!1),[q,K]=(0,s.useState)({isOpen:!1,chatId:"",chatName:"",action:"access"}),G=(0,s.useRef)(!1),fetchOpenRouterBalance=async()=>{if(G.current){console.log("Requisi\xe7\xe3o de saldo j\xe1 em andamento, ignorando...");return}try{G.current=!0,console.log("Iniciando busca do saldo do OpenRouter..."),y(e=>({...e,isLoading:!0,error:void 0}));let e="";try{let t=(0,i.collection)(l.db,"usuarios",n.username,"endpoints"),a=await (0,i.getDocs)(t);a.forEach(t=>{let a=t.data();a.isActive&&a.url&&a.url.includes("openrouter.ai")&&(e=a.apiKey)})}catch(e){console.log("Nova estrutura n\xe3o encontrada, tentando estrutura antiga...")}if(!e)try{let{doc:t,getDoc:r}=await Promise.resolve().then(a.bind(a,4086)),s=t(l.db,"usuarios",n.username,"configuracoes","settings"),o=await r(s);if(o.exists()){let t=o.data();t.endpoints&&Object.values(t.endpoints).forEach(t=>{t.ativo&&t.url&&t.url.includes("openrouter.ai")&&(e=t.apiKey)})}}catch(e){console.log("Estrutura antiga tamb\xe9m n\xe3o encontrada")}if(!e){y(e=>({...e,isLoading:!1,error:"Nenhuma API key do OpenRouter configurada"}));return}console.log("API Key encontrada, buscando saldo...");let t=await fetch("/api/openrouter/credits",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:e})}),r=await t.json();console.log("Resposta da API:",r),r.success?(y({balance:r.balance,isLoading:!1,error:void 0}),console.log("Saldo carregado com sucesso:",r.balance)):(y(e=>({...e,isLoading:!1,error:r.error||"Erro ao buscar saldo"})),console.log("Erro ao buscar saldo:",r.error))}catch(e){console.error("Erro ao buscar saldo do OpenRouter:",e),y(e=>({...e,isLoading:!1,error:"Erro ao conectar com OpenRouter"}))}finally{G.current=!1}},loadChats=async()=>{try{let e=(0,i.collection)(l.db,"usuarios",n.username,"conversas"),t=(0,i.query)(e,(0,i.Xo)("lastUpdatedAt","desc")),a=await (0,i.getDocs)(t),r=[];a.forEach(e=>{let t=e.data();r.push({id:e.id,name:t.name||"Conversa sem nome",lastMessage:t.ultimaMensagem||"Nenhuma mensagem ainda",lastMessageTime:t.ultimaMensagemEm||t.createdAt,folder:t.folderId,password:t.password})});let s=(0,i.collection)(l.db,"usuarios",n.username,"pastas"),o=(0,i.query)(s,(0,i.Xo)("createdAt","asc")),d=await (0,i.getDocs)(o),c=[];d.forEach(e=>{let t=e.data(),a=r.filter(t=>t.folder===e.id);c.push({id:e.id,name:t.name,description:t.description,color:t.color||"#3B82F6",isExpanded:!1!==t.expandedByDefault,chats:a})});let m=r.filter(e=>!e.folder);f(c),j(m)}catch(e){console.error("Erro ao carregar chats e pastas:",e)}},Z=(0,s.useRef)(!1);(0,s.useEffect)(()=>{n.username&&(loadChats(),Z.current||(console.log("Carregando saldo do OpenRouter pela primeira vez..."),fetchOpenRouterBalance(),Z.current=!0))},[n.username]),(0,s.useImperativeHandle)(t,()=>({reloadChats:loadChats,updateOpenRouterBalance:fetchOpenRouterBalance}));let handleEditFolder=async e=>{try{var t;let a=await (0,i.getDocs)((0,i.query)((0,i.collection)(l.db,"usuarios",n.username,"pastas"))),r=null===(t=a.docs.find(t=>t.id===e))||void 0===t?void 0:t.data();r&&(F({id:e,name:r.name,description:r.description,color:r.color||"#3B82F6",expandedByDefault:!1!==r.expandedByDefault}),I(!0))}catch(e){console.error("Erro ao carregar dados da pasta:",e),alert("Erro ao carregar dados da pasta.")}},handleDeleteFolder=(e,t)=>{H({isOpen:!0,type:"folder",id:e,name:t})},handleDeleteChat=(e,t)=>{H({isOpen:!0,type:"chat",id:e,name:t})},deleteChatAttachments=async e=>{try{let t=(0,d.iH)(l.tO,"usuarios/".concat(n.username,"/conversas/").concat(e,"/anexos")),a=await (0,d.aF)(t),r=a.items.map(e=>(0,d.oq)(e));await Promise.all(r),console.log("".concat(a.items.length," anexos deletados do chat ").concat(e))}catch(e){console.log("Erro ao deletar anexos ou pasta de anexos n\xe3o encontrada:",e)}},confirmDelete=async()=>{V(!0);try{if("folder"===U.type){var e;await (0,i.oe)((0,i.doc)(l.db,"usuarios",n.username,"pastas",U.id));let t=(null===(e=b.find(e=>e.id===U.id))||void 0===e?void 0:e.chats)||[];for(let e of t)await (0,i.r7)((0,i.doc)(l.db,"usuarios",n.username,"conversas",e.id),{folderId:null,updatedAt:new Date().toISOString()});console.log("Pasta deletada:",U.id)}else{await (0,i.oe)((0,i.doc)(l.db,"usuarios",n.username,"conversas",U.id));try{let e=(0,d.iH)(l.tO,"usuarios/".concat(n.username,"/conversas/").concat(U.id,"/chat.json"));await (0,d.oq)(e)}catch(e){console.log("Arquivo chat.json no storage n\xe3o encontrado:",e)}await deleteChatAttachments(U.id),x===U.id&&h(""),console.log("Chat deletado:",U.id)}loadChats(),H({isOpen:!1,type:"chat",id:"",name:""})}catch(e){console.error("Erro ao deletar:",e),alert("Erro ao deletar. Tente novamente.")}finally{V(!1)}},handleDragStart=e=>{z(e)},handleDragEnd=()=>{z(null),R(null)},handleDragOver=(e,t)=>{e.preventDefault(),R(t)},handleDragLeave=()=>{R(null)},handleDrop=async(e,t)=>{if(e.preventDefault(),P)try{await (0,i.r7)((0,i.doc)(l.db,"usuarios",n.username,"conversas",P),{folderId:t,updatedAt:new Date().toISOString()}),console.log("Chat ".concat(P," movido para pasta ").concat(t||"sem pasta")),loadChats()}catch(e){console.error("Erro ao mover chat:",e)}finally{z(null),R(null)}},handleEditChat=e=>{E(e),M(!0)},toggleFolder=e=>{f(t=>t.map(t=>t.id===e?{...t,isExpanded:!t.isExpanded}:t))},formatTime=e=>{let t=new Date(e),a=new Date,r=(a.getTime()-t.getTime())/36e5;return r<24?t.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}):r<168?t.toLocaleDateString("pt-BR",{weekday:"short"}):t.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"})},getFolderHexColor=e=>({blue:"#3B82F6",green:"#10B981",yellow:"#F59E0B",red:"#EF4444",purple:"#8B5CF6",cyan:"#06B6D4",lime:"#84CC16",orange:"#F97316",pink:"#EC4899",gray:"#6B7280"})[e]||e,checkChatPassword=async(e,t)=>{try{var a;let r=await (0,i.getDocs)((0,i.query)((0,i.collection)(l.db,"usuarios",n.username,"conversas"))),s=null===(a=r.docs.find(t=>t.id===e))||void 0===a?void 0:a.data();if(s&&s.password)return s.password===t;return!0}catch(e){return console.error("Erro ao verificar senha:",e),!1}},handleProtectedAction=(e,t,a)=>{let r=[...v,...b.flatMap(e=>e.chats)].find(t=>t.id===e);(null==r?void 0:r.password)?K({isOpen:!0,chatId:e,chatName:t,action:a}):executeAction(e,t,a)},executeAction=(e,t,a)=>{switch(a){case"access":h(e);break;case"edit":let r=[...v,...b.flatMap(e=>e.chats)].find(t=>t.id===e);r&&handleEditChat(r);break;case"delete":handleDeleteChat(e,t)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\n        ".concat(o?"translate-x-0":"-translate-x-full","\n        ").concat(c?"lg:-translate-x-full":"lg:translate-x-0","\n      "),children:[g&&(0,r.jsx)("button",{onClick:m,className:"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-500 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/20 z-10 group",title:"Fechar sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-180",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("div",{className:"p-6 border-b border-white/10",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-lg",children:n.username.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-white font-semibold text-lg",children:n.username}),w.isLoading?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("p",{className:"text-blue-200 text-sm",children:"Carregando..."})]}):w.error?(0,r.jsx)("p",{className:"text-red-400 text-sm",title:w.error,children:"$0.00"}):(0,r.jsxs)("p",{className:"text-blue-200 text-sm",children:["$",w.balance.toFixed(4)]})]}),(0,r.jsx)("button",{onClick:u,className:"text-blue-200 hover:text-white transition-colors p-1",children:(0,r.jsxs)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})})]})}),(0,r.jsxs)("div",{className:"p-4 space-y-2",children:[(0,r.jsxs)("button",{onClick:()=>{h(null)},className:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})}),(0,r.jsx)("span",{children:"\xc1rea Inicial"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{k(!0)},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{children:"Nova Conversa"})]}),(0,r.jsx)("button",{onClick:()=>{D(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium",title:"Nova Pasta",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})})]})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent",children:[(0,r.jsx)("div",{className:"px-4 py-2 border-b border-white/10",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("h4",{className:"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,r.jsx)("span",{children:"Conversas"})]})})}),(0,r.jsx)("div",{className:"px-2 py-2",children:b.map(e=>(0,r.jsxs)("div",{className:"mb-2",onDragOver:t=>handleDragOver(t,e.id),onDragLeave:handleDragLeave,onDrop:t=>handleDrop(t,e.id),children:[(0,r.jsx)("div",{className:"group relative rounded-lg transition-all duration-300 cursor-pointer ".concat(O===e.id?"bg-blue-800/40":"hover:bg-blue-800/30"," ").concat(B===e.id?"bg-blue-500/30 ring-2 ring-blue-400/50":""),onMouseEnter:()=>W(e.id),onMouseLeave:()=>W(null),onClick:()=>toggleFolder(e.id),children:(0,r.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-300 transition-transform duration-200 ".concat(e.isExpanded?"rotate-90":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),(0,r.jsx)("div",{className:"w-6 h-6 rounded flex items-center justify-center flex-shrink-0",style:{backgroundColor:getFolderHexColor(e.color)+"40"},children:(0,r.jsx)("svg",{className:"w-4 h-4",style:{color:getFolderHexColor(e.color)},fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("h4",{className:"text-base font-semibold text-blue-100 truncate",children:e.name}),(0,r.jsx)("span",{className:"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full",children:e.chats.length})]}),e.description&&(0,r.jsx)("p",{className:"text-xs text-blue-300/60 truncate mt-0.5",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 transition-all duration-300 ".concat(O===e.id?"opacity-100 translate-x-0":"opacity-0 translate-x-2"),children:[(0,r.jsx)("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200",title:"Editar pasta",onClick:t=>{t.stopPropagation(),handleEditFolder(e.id)},children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsx)("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200",title:"Deletar pasta",onClick:t=>{t.stopPropagation(),handleDeleteFolder(e.id,e.name)},children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})}),e.isExpanded&&(0,r.jsx)("div",{className:"ml-6 mt-2 space-y-1",children:e.chats.map(e=>(0,r.jsxs)("div",{draggable:!0,onDragStart:()=>handleDragStart(e.id),onDragEnd:handleDragEnd,className:"group relative rounded-xl transition-all duration-300 cursor-move ".concat(x===e.id?"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg":"hover:bg-blue-800/30 border border-transparent"," ").concat(P===e.id?"opacity-50 scale-95":""),children:[(0,r.jsxs)("button",{className:"w-full text-left p-3 flex items-start space-x-3",onClick:()=>handleProtectedAction(e.id,e.name,"access"),children:[(0,r.jsxs)("div",{className:"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ".concat(x===e.id?"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30":"bg-blue-700/50 group-hover:bg-blue-600/70"),children:[e.password&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900",children:(0,r.jsx)("svg",{className:"w-2 h-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})]}),(0,r.jsxs)("div",{className:"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20",children:[(0,r.jsx)("h4",{className:"truncate text-sm font-semibold mb-1 ".concat(x===e.id?"text-white":"text-blue-100 group-hover:text-white"),children:e.name}),(0,r.jsx)("p",{className:"truncate text-xs leading-relaxed transition-all duration-300 ".concat(x===e.id?"text-blue-200/80":"text-blue-300/70 group-hover:text-blue-200"),children:e.lastMessage||"Nenhuma mensagem ainda..."}),e.lastMessageTime&&(0,r.jsx)("span",{className:"text-xs mt-1 block ".concat(x===e.id?"text-blue-300/60":"text-blue-400/50 group-hover:text-blue-300/70"),children:formatTime(e.lastMessageTime)})]})]}),(0,r.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300",children:[(0,r.jsx)("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm",title:"Editar",onClick:t=>{t.stopPropagation(),handleProtectedAction(e.id,e.name,"edit")},children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsx)("button",{className:"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm",title:"Deletar",onClick:t=>{t.stopPropagation(),handleProtectedAction(e.id,e.name,"delete")},children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]},e.id))})]},e.id))}),(0,r.jsxs)("div",{className:"px-2 py-2",onDragOver:e=>handleDragOver(e,null),onDragLeave:handleDragLeave,onDrop:e=>handleDrop(e,null),children:[(0,r.jsx)("div",{className:"px-3 py-2",children:(0,r.jsxs)("h5",{className:"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 ".concat(null===B&&P?"text-blue-400":""),children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,r.jsxs)("span",{children:["Sem Pasta ",null===B&&P?"(Solte aqui para remover da pasta)":""]})]})}),(0,r.jsx)("div",{className:"space-y-1 min-h-[60px] transition-all duration-200 ".concat(null===B&&P?"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50":""),children:0===v.length?(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white/30",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-white/40 text-xs",children:"Nenhuma conversa sem pasta"})]}):v.map(e=>(0,r.jsx)(ChatItem,{chat:e,isActive:x===e.id,onClick:()=>handleProtectedAction(e.id,e.name,"access"),onEdit:(e,t)=>handleProtectedAction(e,t,"edit"),onDelete:(e,t)=>handleProtectedAction(e,t,"delete"),onDragStart:handleDragStart,onDragEnd:handleDragEnd,isDragging:P===e.id},e.id))})]}),0===b.length&&0===v.length&&(0,r.jsxs)("div",{className:"px-4 py-8 text-center",children:[(0,r.jsx)("div",{className:"text-white/30 mb-2",children:(0,r.jsx)("svg",{className:"w-12 h-12 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-white/50 text-sm",children:"Nenhuma conversa ainda"}),(0,r.jsx)("p",{className:"text-white/30 text-xs mt-1",children:'Clique em "Nova Conversa" para come\xe7ar'})]})]}),(0,r.jsx)("div",{className:"lg:hidden p-4 border-t border-white/10",children:(0,r.jsxs)("button",{onClick:m,className:"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,r.jsx)("span",{children:"Fechar"})]})})]})]}),(0,r.jsx)(CreateChatModal,{isOpen:N,onClose:()=>k(!1),username:n.username,onChatCreated:e=>{console.log("Chat criado:",e),loadChats(),h(e)}}),S&&(0,r.jsx)(CreateChatModal,{isOpen:C,onClose:()=>{M(!1),E(null)},username:n.username,onChatCreated:e=>{console.log("Chat atualizado:",e),loadChats(),M(!1),E(null)},editingChat:S}),(0,r.jsx)(CreateFolderModal,{isOpen:L,onClose:()=>D(!1),username:n.username,onFolderCreated:e=>{console.log("Pasta criada:",e),loadChats()}}),(0,r.jsx)(CreateFolderModal,{isOpen:A,onClose:()=>{I(!1),F(null)},username:n.username,onFolderCreated:e=>{console.log("Pasta atualizada:",e),loadChats(),I(!1),F(null)},editingFolder:T}),(0,r.jsx)(ConfirmDeleteModal,{isOpen:U.isOpen,onClose:()=>H({isOpen:!1,type:"chat",id:"",name:""}),onConfirm:confirmDelete,title:"folder"===U.type?"Deletar Pasta":"Deletar Conversa",message:"folder"===U.type?'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\xe3o movidas para "Sem Pasta".':"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\xe3o perdidas permanentemente.",itemName:U.name,isLoading:_}),(0,r.jsx)(PasswordProtectedModal,{isOpen:q.isOpen,onClose:()=>K({isOpen:!1,chatId:"",chatName:"",action:"access"}),onSuccess:()=>{executeAction(q.chatId,q.chatName,q.action),K({isOpen:!1,chatId:"",chatName:"",action:"access"})},chatName:q.chatName,onPasswordSubmit:e=>checkChatPassword(q.chatId,e)})]})});function ChatItem(e){let{chat:t,isActive:a,onClick:s,onEdit:n,onDelete:o,onDragStart:l,onDragEnd:i,isDragging:d}=e;return(0,r.jsxs)("div",{draggable:!0,onDragStart:()=>l(t.id),onDragEnd:i,className:"relative w-full rounded-lg transition-all duration-200 group cursor-move ".concat(a?"bg-blue-600/50":"hover:bg-white/5"," ").concat(d?"opacity-50 scale-95":""),children:[(0,r.jsx)("button",{onClick:s,className:"w-full text-left px-3 py-3 text-white/80 hover:text-white",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsxs)("div",{className:"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative ".concat(a?"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30":"bg-blue-700/50 group-hover:bg-blue-600/70"),children:[t.password&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900",children:(0,r.jsx)("svg",{className:"w-2 h-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2,children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("h6",{className:"font-medium text-sm truncate",children:t.name}),(0,r.jsx)("span",{className:"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200",children:(e=>{let t=new Date(e),a=new Date,r=(a.getTime()-t.getTime())/36e5;return r<24?t.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}):r<168?"h\xe1 ".concat(Math.floor(r/24)," dias"):t.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"})})(t.lastMessageTime)})]}),(0,r.jsx)("p",{className:"text-xs text-white/60 line-clamp-2 leading-relaxed",children:t.lastMessage})]})]})}),(0,r.jsxs)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1",children:[(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),n(t.id,t.name)},className:"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200",title:"Configurar chat",children:(0,r.jsxs)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),o(t.id,t.name)},className:"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200",title:"Deletar chat",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})}h.displayName="Sidebar";let useSessionTime=e=>{let[t,a]=(0,s.useState)("0s"),[r]=(0,s.useState)(Date.now()),n=(0,s.useRef)(null),formatTime=e=>{let t=Math.floor(e/1e3),a=Math.floor(t/60),r=Math.floor(a/60),s=Math.floor(r/24),n=Math.floor(s/30),o=t%60,l=a%60,i=r%24,d=s%30;return 0===a?"".concat(t,"s"):0===r?"".concat(l,":").concat(o.toString().padStart(2,"0")):0===s?"".concat(i,":").concat(l.toString().padStart(2,"0"),":").concat(o.toString().padStart(2,"0")):0===n?"".concat(d,"d ").concat(i.toString().padStart(2,"0"),":").concat(l.toString().padStart(2,"0")):"".concat(n,"m ").concat(d,"d ").concat(i.toString().padStart(2,"0"),":").concat(l.toString().padStart(2,"0"))};return(0,s.useEffect)(()=>(e&&(n.current=setInterval(()=>{let e=Date.now()-r,t=formatTime(e);a(t)},1e3)),()=>{n.current&&(clearInterval(n.current),n.current=null)}),[e,r]),t};function Upperbar(e){let{currentChat:t,chatName:a,aiModel:s,onDownload:n,onAttachments:o,onStatistics:l,isLoading:i=!1,attachmentsCount:d=0,aiMetadata:c}=e,m=useSessionTime(t),u=a||(t?"Chat ".concat(t):"Nova Conversa");return(0,r.jsxs)("div",{className:"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"}),(0,r.jsxs)("div",{className:"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-blue-100 truncate",children:s||"GPT-4.1 Nano"}),(null==c?void 0:c.usedCoT)&&(0,r.jsx)("div",{className:"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block",children:"CoT"})]}),(0,r.jsx)("div",{className:"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex",children:[(0,r.jsx)("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-blue-200 font-mono",children:m})]})]}),(0,r.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 hidden sm:block",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm",children:[(0,r.jsx)("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full shadow-lg flex-shrink-0 ".concat(i?"bg-yellow-400 shadow-yellow-400/50 animate-pulse":"bg-cyan-400 shadow-cyan-400/50")}),(0,r.jsx)("h1",{className:"text-sm sm:text-lg font-semibold text-white truncate",children:u})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>{o?o():console.log("Anexos clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative",title:"Anexos",children:[(0,r.jsx)("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})}),d>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg",children:d>99?"99+":d})]}),(0,r.jsx)("button",{onClick:()=>{l?l():console.log("Estat\xedsticas clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105",title:"Estat\xedsticas",children:(0,r.jsx)("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,r.jsx)("button",{onClick:()=>{n?n():console.log("Download clicado")},className:"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30",title:"Download",children:(0,r.jsx)("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})]})]})]})}var x=a(9285),p=a(1032),g=a(1415),b=a(3432),f=a(1512);a(9792),a(576),a(8929);let v=s.memo(e=>{let{content:t,isStreaming:a}=e;return(0,r.jsx)(x.UG,{remarkPlugins:[p.Z,g.Z],rehypePlugins:[b.Z,[f.Z,{detect:!0,ignoreMissing:!0}]],components:{code(e){let{node:t,inline:a,className:s,children:n,...o}=e,l=/language-(\w+)/.exec(s||"");return!a&&l?(0,r.jsx)("pre",{className:"bg-gray-800 rounded-lg p-4 overflow-x-auto",children:(0,r.jsx)("code",{className:s,...o,children:n})}):(0,r.jsx)("code",{className:"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono",...o,children:n})},a(e){let{children:t,href:a,...s}=e;return(0,r.jsx)("a",{href:a,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 underline",...s,children:t})},table(e){let{children:t,...a}=e;return(0,r.jsx)("div",{className:"overflow-x-auto my-4",children:(0,r.jsx)("table",{className:"min-w-full border-collapse border border-gray-600",...a,children:t})})},th(e){let{children:t,...a}=e;return(0,r.jsx)("th",{className:"border border-gray-600 px-4 py-2 bg-gray-700 text-left font-semibold",...a,children:t})},td(e){let{children:t,...a}=e;return(0,r.jsx)("td",{className:"border border-gray-600 px-4 py-2",...a,children:t})},blockquote(e){let{children:t,...a}=e;return(0,r.jsx)("blockquote",{className:"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-900/20 rounded-r-lg",...a,children:t})},ul(e){let{children:t,...a}=e;return(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1 my-2",...a,children:t})},ol(e){let{children:t,...a}=e;return(0,r.jsx)("ol",{className:"list-decimal list-inside space-y-1 my-2",...a,children:t})},h1(e){let{children:t,...a}=e;return(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4 mt-6 text-white border-b border-gray-600 pb-2",...a,children:t})},h2(e){let{children:t,...a}=e;return(0,r.jsx)("h2",{className:"text-xl font-bold mb-3 mt-5 text-white",...a,children:t})},h3(e){let{children:t,...a}=e;return(0,r.jsx)("h3",{className:"text-lg font-bold mb-2 mt-4 text-white",...a,children:t})},p(e){let{children:t,...a}=e;return(0,r.jsx)("p",{className:"mb-3 leading-relaxed text-gray-100",...a,children:t})}},children:t})},(e,t)=>{if(t.isStreaming){let a=e.content.length,r=t.content.length;return!(r-a>=100||r<a)}return e.content===t.content}),j=s.memo(e=>{let{content:t,className:a="",hasWebSearch:n=!1,webSearchAnnotations:o=[],isStreaming:l=!1}=e,detectWebSearch=e=>{let t=e.match(/\[[\w.-]+\.[\w]+\]/g);return null!==t&&t.length>0},processWebSearchLinks=e=>e,getWebSearchInfo=(e,t)=>{if(t.length>0){let e=new Set(t.map(e=>{try{return new URL(e.url).hostname.replace("www.","")}catch(t){return e.url}}));return{sourceCount:t.length,sources:Array.from(e)}}let a=e.match(/\[[\w.-]+\.[\w]+\]\([^)]+\)/g)||[],r=new Set(a.map(e=>{let t=e.match(/\[([\w.-]+\.[\w]+)\]/);return t?t[1]:e})),s=Array.from(r);return{sourceCount:a.length,sources:s}},{isWebSearchMessage:i,webSearchInfo:d,processedContent:c}=(0,s.useMemo)(()=>{let e=n||detectWebSearch(t),a=e?getWebSearchInfo(t,o):{sourceCount:0,sources:[]},r=e?processWebSearchLinks(t):t;return{isWebSearchMessage:e,webSearchInfo:a,processedContent:r}},[t,n,o]);return(0,r.jsxs)("div",{className:"markdown-content ".concat(a," ").concat(l?"streaming-mode":""),children:[i&&(0,r.jsxs)("div",{className:"web-search-section mb-4 p-3 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z"})}),(0,r.jsx)("span",{className:"text-cyan-300 text-sm font-medium",children:"\uD83C\uDF10 Busca na Web Ativada"})]}),(0,r.jsxs)("div",{className:"text-xs text-cyan-200/80 leading-relaxed",children:[(0,r.jsxs)("span",{className:"text-cyan-300 font-medium",children:[d.sourceCount," cita\xe7\xe3o",1!==d.sourceCount?"\xf5es":""," de ",d.sources.length," fonte",1!==d.sources.length?"s":""]}),d.sources.length>0&&(0,r.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:d.sources.map((e,t)=>(0,r.jsx)("span",{className:"inline-block px-2 py-0.5 bg-cyan-600/20 text-cyan-200 rounded text-xs border border-cyan-500/30",children:e},t))})]})]}),(0,r.jsx)(v,{content:c,isStreaming:l})]})});var w=a(8438),y=a(9670),N=a(5817),k=a(6637);function AttachmentDisplay(e){let{attachments:t,isUserMessage:a=!1}=e,[n,o]=(0,s.useState)(null);if(!t||0===t.length)return null;let handleImageClick=e=>{o(e)},handleDownload=async e=>{try{let t=await fetch(e.url),a=await t.blob(),r=window.URL.createObjectURL(a),s=document.createElement("a");s.href=r,s.download=e.filename,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(r),document.body.removeChild(s)}catch(e){console.error("Erro ao baixar arquivo:",e)}},formatFileSize=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,r.jsxs)("div",{className:"mt-2 space-y-2",children:[t.map(e=>(0,r.jsx)("div",{className:"\n            border rounded-lg p-3 max-w-sm\n            ".concat(a?"bg-blue-50 border-blue-200":"bg-gray-50 border-gray-200","\n          "),children:"image"===e.type?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(w.Z,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 truncate",children:e.filename}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:formatFileSize(e.size)})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("img",{src:e.url,alt:e.filename,className:"max-w-full h-auto rounded cursor-pointer hover:opacity-90 transition-opacity",style:{maxHeight:"200px"},onClick:()=>handleImageClick(e.url)}),(0,r.jsx)("button",{onClick:()=>handleImageClick(e.url),className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70 transition-all",title:"Expandir imagem",children:(0,r.jsx)(y.Z,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(N.Z,{className:"w-3 h-3"}),"Baixar"]})})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(k.Z,{className:"w-4 h-4 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 truncate",children:e.filename}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:formatFileSize(e.size)})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(N.Z,{className:"w-3 h-3"}),"Baixar PDF"]})})]})},e.id)),n&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4",onClick:()=>o(null),children:(0,r.jsxs)("div",{className:"relative max-w-full max-h-full",children:[(0,r.jsx)("img",{src:n,alt:"Imagem expandida",className:"max-w-full max-h-full object-contain"}),(0,r.jsx)("button",{onClick:()=>o(null),className:"absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all",children:"✕"})]})})]})}function DeleteMessageModal(e){let{isOpen:t,onClose:a,onConfirm:s,messagePreview:n,isDeleting:o=!1}=e;if(!t)return null;let l=n.length>100?n.substring(0,100)+"...":n;return(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:a}),(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900 border border-red-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-600/20 to-red-500/20 border-b border-red-500/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center border border-red-500/30",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Confirmar Exclus\xe3o"}),(0,r.jsx)("p",{className:"text-red-300/70 text-sm",children:"Esta a\xe7\xe3o n\xe3o pode ser desfeita"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed",children:"Tem certeza que deseja excluir esta mensagem permanentemente?"}),(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-600/30 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-xs text-gray-400 mb-1",children:"Mensagem:"}),(0,r.jsxs)("p",{className:"text-gray-200 text-sm leading-relaxed break-words",children:['"',l,'"']})]}),(0,r.jsx)("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-red-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-300/80 text-xs",children:"A mensagem ser\xe1 removida permanentemente do chat e n\xe3o poder\xe1 ser recuperada."})]})})]}),(0,r.jsx)("div",{className:"bg-gray-800/30 border-t border-gray-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:a,disabled:o,className:"px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),(0,r.jsx)("button",{onClick:s,disabled:o,className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 border border-red-500/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:"Excluindo..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),(0,r.jsx)("span",{children:"Excluir"})]})})]})})]})]})}function RegenerateMessageModal(e){let{isOpen:t,onClose:a,onConfirm:s,messagePreview:n,messagesAffectedCount:o,isRegenerating:l=!1}=e;if(!t)return null;let i=n.length>100?n.substring(0,100)+"...":n;return(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/60 backdrop-blur-sm",onClick:a}),(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900 border border-blue-500/30 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600/20 to-cyan-500/20 border-b border-blue-500/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Regenerar Mensagem"}),(0,r.jsx)("p",{className:"text-blue-300/70 text-sm",children:"Confirme para continuar"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-300 text-sm leading-relaxed",children:["Deseja regenerar a resposta para esta mensagem?",o>0&&(0,r.jsxs)("span",{className:"text-orange-300",children:[" ","Isso remover\xe1 ",o," mensagem",o>1?"s":""," que ",o>1?"v\xeam":"vem"," depois desta."]})]}),(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-600/30 rounded-lg p-3",children:[(0,r.jsx)("p",{className:"text-xs text-gray-400 mb-1",children:"Mensagem que ser\xe1 enviada novamente:"}),(0,r.jsxs)("p",{className:"text-gray-200 text-sm leading-relaxed break-words",children:['"',i,'"']})]}),o>0&&(0,r.jsx)("div",{className:"bg-orange-900/20 border border-orange-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-orange-300/80 text-xs font-medium mb-1",children:"Aten\xe7\xe3o: Mensagens posteriores ser\xe3o removidas"}),(0,r.jsxs)("p",{className:"text-orange-300/70 text-xs",children:[o," mensagem",o>1?"s":""," que ",o>1?"vieram":"veio"," depois desta ser\xe1",o>1?"m":""," permanentemente removida",o>1?"s":""," para manter a consist\xeancia da conversa."]})]})]})}),(0,r.jsx)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-blue-300/80 text-xs",children:"A IA gerar\xe1 uma nova resposta para esta mensagem. A mensagem original ser\xe1 mantida e apenas as respostas posteriores ser\xe3o removidas."})]})})]}),(0,r.jsx)("div",{className:"bg-gray-800/30 border-t border-gray-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:a,disabled:l,className:"px-4 py-2 text-sm font-medium text-gray-300 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancelar"}),(0,r.jsx)("button",{onClick:s,disabled:l,className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 border border-blue-500/30 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"w-4 h-4 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:"Regenerando..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,r.jsx)("span",{children:"Regenerar"})]})})]})})]})]})}function ChatInterface(e){let{messages:t,isLoading:a,isLoadingChat:n,isStreaming:o=!1,streamingMessageId:l,onDeleteMessage:i,onRegenerateMessage:d,onEditMessage:c,onCopyMessage:m}=e,u=(0,s.useRef)(null),[h,x]=(0,s.useState)(null),[p,g]=(0,s.useState)(""),[b,f]=(0,s.useState)({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1}),[v,w]=(0,s.useState)({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1}),scrollToBottom=()=>{requestAnimationFrame(()=>{var e;null===(e=u.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})})};(0,s.useEffect)(()=>{let e=setTimeout(()=>{scrollToBottom()},100);return()=>clearTimeout(e)},[t]);let formatTime=e=>new Date(e).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"}),handleStartEdit=e=>{x(e.id),g(e.content)},handleSaveEdit=()=>{h&&""!==p.trim()&&c(h,p.trim()),x(null),g("")},handleCancelEdit=()=>{x(null),g("")},handleDeleteClick=e=>{f({isOpen:!0,messageId:e.id,messageContent:e.content,isDeleting:!1})},handleDeleteConfirm=async()=>{f(e=>({...e,isDeleting:!0}));try{await i(b.messageId),f({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1})}catch(e){console.error("Erro ao deletar mensagem:",e),f(e=>({...e,isDeleting:!1}))}},handleRegenerateClick=e=>{let a=t.findIndex(t=>t.id===e.id),r=t.length-a-1;w({isOpen:!0,messageId:e.id,messageContent:e.content,messagesAffectedCount:r,isRegenerating:!1})},handleRegenerateConfirm=async()=>{w({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1});try{d(v.messageId)}catch(e){console.error("Erro ao regenerar mensagem:",e)}},MessageActions=e=>{let{message:t,isUser:a}=e;return(0,r.jsxs)("div",{className:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)("button",{onClick:()=>handleDeleteClick(t),className:"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110",title:"Excluir mensagem",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})}),a&&(0,r.jsx)("button",{onClick:()=>handleRegenerateClick(t),className:"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110",title:"Regenerar mensagem",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),(0,r.jsx)("button",{onClick:()=>handleStartEdit(t),className:"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110",title:"Editar mensagem",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsx)("button",{onClick:()=>m(t.content),className:"p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110",title:"Copiar mensagem",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})};return(0,r.jsxs)("div",{className:"h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent",style:{maxHeight:"100%"},children:[n?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm",children:(0,r.jsx)("svg",{className:"w-10 h-10 text-blue-400 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent",children:"Carregando mensagens"}),(0,r.jsx)("p",{className:"text-white/60 text-base leading-relaxed",children:"Aguarde enquanto carregamos o hist\xf3rico da conversa..."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)("div",{className:"w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden",children:(0,r.jsx)("div",{className:"h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse"})})})]})]})}):0===t.length?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm",children:(0,r.jsx)("svg",{className:"w-10 h-10 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent",children:"Comece uma nova conversa"}),(0,r.jsx)("p",{className:"text-white/60 text-base leading-relaxed",children:"Digite sua mensagem abaixo para come\xe7ar a conversar com a IA"}),(0,r.jsxs)("div",{className:"mt-8 grid grid-cols-1 gap-3",children:[(0,r.jsx)("div",{className:"bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"text-white/80 text-sm font-medium",children:"Fa\xe7a perguntas"}),(0,r.jsx)("p",{className:"text-white/50 text-xs",children:"Tire d\xfavidas sobre qualquer assunto"})]})]})}),(0,r.jsx)("div",{className:"bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-cyan-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"text-white/80 text-sm font-medium",children:"Pe\xe7a ajuda com c\xf3digo"}),(0,r.jsx)("p",{className:"text-white/50 text-xs",children:"Programa\xe7\xe3o, debugging e explica\xe7\xf5es"})]})]})}),(0,r.jsx)("div",{className:"bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"text-white/80 text-sm font-medium",children:"Crie conte\xfado"}),(0,r.jsx)("p",{className:"text-white/50 text-xs",children:"Textos, resumos e ideias criativas"})]})]})})]})]})]})}):(0,r.jsxs)("div",{className:"space-y-4 min-h-0",children:[t.map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-2 group animate-message-slide-in ".concat("user"===e.sender?"flex-row-reverse space-x-reverse":""),children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 ".concat("user"===e.sender?"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30":"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30"),children:(0,r.jsx)("span",{className:"text-white text-sm font-bold drop-shadow-sm",children:"user"===e.sender?"U":"AI"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"backdrop-blur-sm rounded-2xl p-3 max-w-3xl border ".concat("user"===e.sender?"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto":"bg-blue-600/20 border-blue-500/20 rounded-tl-md"),children:h===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:p,onChange:e=>g(e.target.value),className:"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50",placeholder:"Digite sua mensagem...",autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:handleCancelEdit,className:"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200",children:"Cancelar"}),(0,r.jsx)("button",{onClick:handleSaveEdit,className:"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200",children:"Salvar"})]})]}):(0,r.jsxs)("div",{children:[e.attachments&&e.attachments.length>0&&(0,r.jsx)(AttachmentDisplay,{attachments:e.attachments,isUserMessage:"user"===e.sender}),e.content&&(0,r.jsx)(j,{content:e.content,hasWebSearch:e.hasWebSearch,webSearchAnnotations:e.webSearchAnnotations,isStreaming:o&&e.id===l})]})}),(0,r.jsx)("div",{className:"flex items-center gap-3 mt-2 ".concat("user"===e.sender?"justify-end":"justify-start"),children:"user"===e.sender?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(MessageActions,{message:e,isUser:!0}),(0,r.jsx)("p",{className:"text-white/40 text-xs",children:formatTime(e.timestamp)})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{className:"text-white/40 text-xs",children:formatTime(e.timestamp)}),(0,r.jsx)(MessageActions,{message:e,isUser:!1})]})})]})]},e.id)),a&&(0,r.jsxs)("div",{className:"flex items-start space-x-2 animate-message-slide-in",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30",children:(0,r.jsx)("span",{className:"text-white text-sm font-bold drop-shadow-sm",children:"AI"})}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("div",{className:"bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing",style:{animationDelay:"0.2s"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-typing",style:{animationDelay:"0.4s"}})]}),(0,r.jsx)("span",{className:"text-white/60 text-sm animate-pulse",children:"Digitando..."})]})})})]}),(0,r.jsx)("div",{ref:u,className:"h-0"})]}),(0,r.jsx)(DeleteMessageModal,{isOpen:b.isOpen,onClose:()=>{f({isOpen:!1,messageId:"",messageContent:"",isDeleting:!1})},onConfirm:handleDeleteConfirm,messagePreview:b.messageContent,isDeleting:b.isDeleting}),(0,r.jsx)(RegenerateMessageModal,{isOpen:v.isOpen,onClose:()=>{w({isOpen:!1,messageId:"",messageContent:"",messagesAffectedCount:0,isRegenerating:!1})},onConfirm:handleRegenerateConfirm,messagePreview:v.messageContent,messagesAffectedCount:v.messagesAffectedCount,isRegenerating:v.isRegenerating})]})}let C=new class{validateFile(e){if(e.size>this.MAX_FILE_SIZE)return{isValid:!1,error:"Arquivo muito grande. M\xe1ximo permitido: 10MB"};let t=this.SUPPORTED_IMAGE_TYPES.includes(e.type),a=e.type===this.SUPPORTED_PDF_TYPE;return t||a?{isValid:!0}:{isValid:!1,error:"Tipo de arquivo n\xe3o suportado. Use PNG, JPEG, WebP ou PDF"}}async fileToBase64(e){return new Promise((t,a)=>{let r=new FileReader;r.onload=()=>{let e=r.result,a=e.split(",")[1];t(a)},r.onerror=a,r.readAsDataURL(e)})}generateAttachmentId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}async uploadAttachment(e){let{file:t,username:a,chatId:r}=e,s=this.validateFile(t);if(!s.isValid)throw Error(s.error);try{let e;let s=this.generateAttachmentId(),n=this.SUPPORTED_IMAGE_TYPES.includes(t.type),o=n?"image":"pdf",i="usuarios/".concat(a,"/conversas/").concat(r,"/anexos/").concat(s,"_").concat(t.name),c=(0,d.iH)(l.tO,i);await (0,d.KV)(c,t);let m=await (0,d.Jt)(c),u={id:s,type:o,filename:t.name,url:m,size:t.size,uploadedAt:Date.now(),storagePath:i,isActive:!0};return"pdf"===o&&(e=await this.fileToBase64(t),u.base64Data=e),{metadata:u,base64Data:e}}catch(e){throw console.error("Erro ao fazer upload do anexo:",e),Error("Falha no upload do arquivo. Tente novamente.")}}async uploadMultipleAttachments(e,t,a){let r=[];for(let s of e)try{let e=await this.uploadAttachment({file:s,username:t,chatId:a});r.push(e)}catch(e){console.error("Erro ao processar arquivo ".concat(s.name,":"),e)}return r}prepareAttachmentsForOpenRouter(e){let t=[];for(let a of e)if("image"===a.type)t.push({type:"image_url",image_url:{url:a.url}});else if("pdf"===a.type&&a.base64Data){let e="data:application/pdf;base64,".concat(a.base64Data);t.push({type:"file",file:{filename:a.filename,file_data:e}})}return t}preparePDFPlugins(e){let t=e.some(e=>"pdf"===e.type);return t?[{id:"file-parser",pdf:{engine:"mistral-ocr"}}]:[]}async cleanupTemporaryAttachments(e){console.log("Cleanup de anexos tempor\xe1rios:",e.length)}constructor(){this.MAX_FILE_SIZE=10485760,this.SUPPORTED_IMAGE_TYPES=["image/png","image/jpeg","image/webp"],this.SUPPORTED_PDF_TYPE="application/pdf"}},M=[{id:"gpt-4.1-nano",name:"GPT-4.1 Nano",description:"R\xe1pido e eficiente"},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"Mais poderoso"},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",description:"Criativo e preciso"},{id:"gemini-pro",name:"Gemini Pro",description:"Multimodal"}];function InputBar(e){let{message:t,setMessage:a,onSendMessage:n,isLoading:o,selectedModel:l,onModelChange:i,onScrollToTop:d,onScrollToBottom:c,isStreaming:m=!1,onCancelStreaming:u,onOpenModelModal:h,username:x,chatId:p,activeAttachmentsCount:g=0}=e,[b,f]=(0,s.useState)(!1),[v,j]=(0,s.useState)([]),[w,y]=(0,s.useState)([]),[N,k]=(0,s.useState)(!1),S=(0,s.useRef)(null),E=(0,s.useRef)(null),L=(0,s.useRef)(null);(0,s.useEffect)(()=>()=>{L.current&&clearTimeout(L.current)},[]);let handleSend=()=>{((null==t?void 0:t.trim())||v.length>0)&&!o&&!N&&(console.log("=== DEBUG: ENVIANDO MENSAGEM ==="),console.log("Mensagem:",t),console.log("Anexos locais:",v.length),console.log("Anexos processados:",w.length),console.log("Anexos processados detalhes:",w),console.log("Web Search Enabled:",b),n(w,b),j([]),y([]))},handleFileSelect=async e=>{let t=e.target.files;if(t&&x&&p){k(!0);try{let e=[];for(let a of Array.from(t)){let t={id:Date.now().toString()+Math.random().toString(36).substring(2,9),filename:a.name,type:a.type.startsWith("image/")?"image":"document",file:a};e.push(t)}j(t=>[...t,...e]);let a=await C.uploadMultipleAttachments(Array.from(t),x,p);y(e=>[...e,...a.map(e=>e.metadata)])}catch(e){console.error("Erro ao processar arquivos:",e),j(e=>e.filter(e=>!Array.from(t).some(t=>t.name===e.filename)))}finally{k(!1),E.current&&(E.current.value="")}}},removeAttachment=e=>{let t=v.find(t=>t.id===e);t&&(j(t=>t.filter(t=>t.id!==e)),y(e=>e.filter(e=>e.filename!==t.filename)))},adjustTextareaHeight=()=>{let e=S.current;if(e){let t=e.style.height;e.style.height="auto";let a=Math.min(e.scrollHeight,120)+"px";t!==a&&(e.style.height=a)}},D=M.find(e=>e.id===l),A=D?D.name:l;return v.length,(0,r.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6 border-t border-blue-700/30 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none"}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto relative z-10",children:[v.length>0&&(0,r.jsx)("div",{className:"mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2 sm:gap-3",children:v.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20",children:["image"===e.type?(0,r.jsx)("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}):(0,r.jsx)("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,r.jsx)("span",{className:"text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium",children:e.filename}),(0,r.jsx)("button",{onClick:()=>removeAttachment(e.id),className:"text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},e.id))})}),(0,r.jsxs)("div",{className:"flex items-end space-x-2 sm:space-x-3",children:[(0,r.jsxs)("div",{className:"flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50",children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]"}),(0,r.jsxs)("div",{className:"relative z-10 flex items-end space-x-2 sm:space-x-3 p-3 sm:p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,r.jsx)("button",{onClick:h,className:"p-2 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 hover:scale-105",title:"Selecionar modelo",children:(0,r.jsx)("svg",{className:"w-4 h-4 sm:w-5 sm:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)("button",{onClick:()=>{E.current&&E.current.click()},disabled:N,className:"p-2.5 rounded-xl bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105",title:"Anexar arquivo",children:N?(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-400"}):(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),!["local/","offline/"].some(e=>l.startsWith(e))&&(0,r.jsx)("button",{onClick:()=>{f(!b)},className:"p-2.5 rounded-xl transition-all duration-200 hover:shadow-lg border hover:scale-105 backdrop-blur-sm ".concat(b?"bg-cyan-600/30 hover:bg-cyan-600/40 text-cyan-200 hover:text-cyan-100 border-cyan-500/50 shadow-lg shadow-cyan-500/20":"bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20"),title:"Busca na web - ".concat(b?"Ativada":"Desativada"),children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z"})})})]}),(0,r.jsx)("div",{className:"flex-1 relative",children:(0,r.jsx)("textarea",{ref:S,value:t,onChange:e=>{a(e.target.value),L.current&&clearTimeout(L.current),L.current=setTimeout(()=>{adjustTextareaHeight()},16)},onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),handleSend())},className:"w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm leading-relaxed min-h-[44px] max-h-[120px] selection:bg-blue-500/30",rows:1,placeholder:"Digite sua mensagem aqui... ✨",disabled:o||m})}),m?(0,r.jsx)("button",{onClick:u,className:"p-3 rounded-xl bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-500 hover:via-red-400 hover:to-red-500 text-white transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 border border-red-500/30",title:"Parar gera\xe7\xe3o",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):(0,r.jsx)("button",{onClick:handleSend,disabled:!(null==t?void 0:t.trim())&&0===v.length||o||N||m,className:"p-3 rounded-xl bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100 border border-blue-500/30",title:"Enviar mensagem",children:o?(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"}):(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})})]}),(0,r.jsxs)("div",{className:"absolute -top-8 left-4 flex items-center space-x-2",children:[l&&(0,r.jsxs)("div",{className:"bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg",children:[(0,r.jsx)("span",{className:"text-xs text-blue-300",children:"Modelo: "}),(0,r.jsx)("span",{className:"text-xs text-cyan-300 font-medium",children:A})]}),g>0&&(0,r.jsxs)("div",{className:"bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg",children:[(0,r.jsx)("span",{className:"text-xs text-green-300",children:"Anexos: "}),(0,r.jsx)("span",{className:"text-xs text-green-200 font-medium",children:g})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[d&&(0,r.jsxs)("button",{onClick:d,className:"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95",title:"Ir para o topo",children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"}),(0,r.jsx)("svg",{className:"relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})]}),c&&(0,r.jsxs)("button",{onClick:c,className:"group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95",title:"Ir para o final",children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"}),(0,r.jsx)("svg",{className:"relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M19 14l-7 7m0 0l-7-7m7 7V4"})})]})]})]}),(0,r.jsx)("input",{ref:E,type:"file",multiple:!0,accept:"image/png,image/jpeg,image/webp,application/pdf",onChange:handleFileSelect,className:"hidden"})]})]})}var S=a(4887),E=a(8608),L=a(7304),dashboard_DownloadModal=e=>{let{isOpen:t,onClose:a,messages:n,chatName:o}=e,[l,i]=(0,s.useState)("all"),[d,c]=(0,s.useState)(!1),[m,u]=(0,s.useState)(!1);(0,s.useEffect)(()=>{u(!0)},[]);let processMessageContent=e=>{try{E.TU.setOptions({breaks:!0,gfm:!0});let t=(0,E.TU)(e);if(t instanceof Promise)return e.replace(/\n/g,"<br>");return t=L.Z.sanitize(t)}catch(t){return console.error("Erro ao processar conte\xfado:",t),e.replace(/\n/g,"<br>")}},generateStyledHTML=(e,t,a)=>{let r={all:"Todas as mensagens",user:"Mensagens do usu\xe1rio",ai:"Mensagens da IA"};return'<!DOCTYPE html>\n<html lang="pt-BR">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>'.concat(t," - ").concat(r[a],'</title>\n\n    <!-- Google Fonts -->\n    <link rel="preconnect" href="https://fonts.googleapis.com">\n    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>\n    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">\n\n    <!-- KaTeX CSS -->\n    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">\n\n    <!-- Highlight.js CSS -->\n    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">\n\n    <!-- KaTeX JavaScript -->\n    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>\n    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>\n\n    <!-- Highlight.js JavaScript -->\n    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>\n\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: \'Inter\', -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, \'Helvetica Neue\', Arial, sans-serif;\n            background: linear-gradient(135deg, #0c1426 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0c1426 100%);\n            background-attachment: fixed;\n            min-height: 100vh;\n            color: #f8fafc;\n            line-height: 1.7;\n            position: relative;\n            overflow-x: hidden;\n        }\n\n        body::before {\n            content: \'\';\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background:\n                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),\n                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),\n                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);\n            pointer-events: none;\n            z-index: -1;\n        }\n\n        .container {\n            max-width: 900px;\n            margin: 0 auto;\n            padding: 3rem 2rem;\n            position: relative;\n            z-index: 1;\n        }\n\n        .header {\n            text-align: center;\n            margin-bottom: 4rem;\n            padding: 3rem 2rem;\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));\n            border-radius: 2rem;\n            border: 1px solid rgba(59, 130, 246, 0.4);\n            backdrop-filter: blur(20px);\n            box-shadow:\n                0 25px 50px -12px rgba(0, 0, 0, 0.5),\n                0 0 0 1px rgba(255, 255, 255, 0.05),\n                inset 0 1px 0 rgba(255, 255, 255, 0.1);\n            position: relative;\n            overflow: hidden;\n        }\n\n        .header::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 1px;\n            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);\n        }\n\n        .header::after {\n            content: \'\';\n            position: absolute;\n            top: -50%;\n            left: -50%;\n            width: 200%;\n            height: 200%;\n            background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);\n            animation: rotate 20s linear infinite;\n            z-index: -1;\n        }\n\n        @keyframes rotate {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n        }\n\n        .header h1 {\n            font-size: 3rem;\n            font-weight: 800;\n            margin-bottom: 1rem;\n            background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 25%, #dbeafe 50%, #93c5fd 75%, #60a5fa 100%);\n            background-size: 200% 200%;\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n            animation: gradientShift 3s ease-in-out infinite;\n            text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);\n            position: relative;\n            z-index: 1;\n        }\n\n        @keyframes gradientShift {\n            0%, 100% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n        }\n\n        .header p {\n            color: rgba(203, 213, 225, 0.9);\n            font-size: 1.2rem;\n            font-weight: 500;\n            position: relative;\n            z-index: 1;\n        }\n        \n        .messages {\n            display: flex;\n            flex-direction: column;\n            gap: 2.5rem;\n        }\n\n        .message {\n            border-radius: 1.5rem;\n            padding: 2rem;\n            border: 1px solid rgba(59, 130, 246, 0.3);\n            backdrop-filter: blur(20px);\n            position: relative;\n            overflow: hidden;\n            transition: all 0.3s ease;\n            box-shadow:\n                0 10px 25px -5px rgba(0, 0, 0, 0.3),\n                0 0 0 1px rgba(255, 255, 255, 0.05);\n        }\n\n        .message::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 2px;\n            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);\n        }\n\n        .message.user {\n            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(34, 197, 94, 0.1));\n            border-color: rgba(16, 185, 129, 0.4);\n            margin-left: 3rem;\n            transform: translateX(0);\n        }\n\n        .message.user::before {\n            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);\n        }\n\n        .message.ai {\n            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.1));\n            border-color: rgba(59, 130, 246, 0.4);\n            margin-right: 3rem;\n            transform: translateX(0);\n        }\n        \n        .message-header {\n            display: flex;\n            align-items: center;\n            margin-bottom: 1.5rem;\n            font-weight: 700;\n            font-size: 1.1rem;\n            position: relative;\n            z-index: 1;\n        }\n\n        .message-header .icon {\n            width: 40px;\n            height: 40px;\n            margin-right: 1rem;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 1.2rem;\n            box-shadow:\n                0 4px 8px rgba(0, 0, 0, 0.2),\n                inset 0 1px 0 rgba(255, 255, 255, 0.2);\n            position: relative;\n            overflow: hidden;\n        }\n\n        .message-header .icon::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);\n            border-radius: 50%;\n        }\n\n        .message.user .icon {\n            background: linear-gradient(135deg, rgba(16, 185, 129, 0.8), rgba(34, 197, 94, 0.6));\n            border: 2px solid rgba(16, 185, 129, 0.5);\n        }\n\n        .message.ai .icon {\n            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.6));\n            border: 2px solid rgba(59, 130, 246, 0.5);\n        }\n        \n        .message-content {\n            background: linear-gradient(135deg, rgba(15, 23, 42, 0.6), rgba(30, 41, 59, 0.4));\n            padding: 2rem;\n            border-radius: 1.25rem;\n            border: 1px solid rgba(59, 130, 246, 0.2);\n            word-wrap: break-word;\n            font-size: 1rem;\n            line-height: 1.8;\n            backdrop-filter: blur(10px);\n            box-shadow:\n                inset 0 1px 0 rgba(255, 255, 255, 0.1),\n                0 4px 6px rgba(0, 0, 0, 0.1);\n            position: relative;\n            overflow: hidden;\n        }\n\n        .message-content::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 1px;\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n        }\n\n        /* ===== ESTILOS PARA MARKDOWN ===== */\n        .message-content h1,\n        .message-content h2,\n        .message-content h3,\n        .message-content h4,\n        .message-content h5,\n        .message-content h6 {\n            font-weight: 700;\n            margin-top: 1.5rem;\n            margin-bottom: 0.75rem;\n            line-height: 1.3;\n            background: linear-gradient(135deg, #60a5fa, #93c5fd, #dbeafe);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n        }\n\n        .message-content h1 { font-size: 1.875rem; border-bottom: 2px solid rgba(59, 130, 246, 0.3); padding-bottom: 0.5rem; }\n        .message-content h2 { font-size: 1.5rem; border-bottom: 1px solid rgba(59, 130, 246, 0.2); padding-bottom: 0.25rem; }\n        .message-content h3 { font-size: 1.25rem; }\n        .message-content h4 { font-size: 1.125rem; }\n        .message-content h5 { font-size: 1rem; }\n        .message-content h6 { font-size: 0.875rem; }\n\n        .message-content p {\n            margin-bottom: 1rem;\n            line-height: 1.7;\n            color: #cbd5e1;\n        }\n\n        .message-content ul,\n        .message-content ol {\n            margin-bottom: 1rem;\n            padding-left: 1.5rem;\n            color: #cbd5e1;\n        }\n\n        .message-content ul li {\n            position: relative;\n            margin-bottom: 0.5rem;\n            list-style: none;\n            padding-left: 1.25rem;\n        }\n\n        .message-content ul li::before {\n            content: \'\';\n            position: absolute;\n            left: 0;\n            top: 0.6rem;\n            width: 6px;\n            height: 6px;\n            background: linear-gradient(135deg, #3b82f6, #60a5fa);\n            border-radius: 50%;\n            transform: translateY(-50%);\n        }\n\n        .message-content ol li {\n            margin-bottom: 0.5rem;\n        }\n\n        .message-content blockquote {\n            margin: 1rem 0;\n            padding: 1rem 1.25rem;\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));\n            border-left: 4px solid #3b82f6;\n            border-radius: 0 0.5rem 0.5rem 0;\n            font-style: italic;\n            color: #e2e8f0;\n        }\n\n        .message-content code {\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));\n            color: #fbbf24;\n            padding: 0.25rem 0.5rem;\n            border-radius: 0.375rem;\n            font-family: \'JetBrains Mono\', \'Fira Code\', \'Monaco\', \'Consolas\', monospace;\n            font-size: 0.875rem;\n            font-weight: 500;\n            border: 1px solid rgba(59, 130, 246, 0.3);\n        }\n\n        .message-content pre {\n            background: linear-gradient(135deg, rgba(13, 28, 74, 0.8), rgba(30, 58, 138, 0.6)) !important;\n            border: 1px solid rgba(59, 130, 246, 0.4);\n            border-radius: 0.75rem;\n            padding: 1.25rem;\n            margin: 1rem 0;\n            overflow-x: auto;\n            position: relative;\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n        }\n\n        .message-content pre::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 3px;\n            background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);\n            border-radius: 0.75rem 0.75rem 0 0;\n        }\n\n        .message-content pre code {\n            background: transparent !important;\n            color: #e2e8f0 !important;\n            padding: 0 !important;\n            border: none !important;\n            font-size: 0.875rem;\n            line-height: 1.5;\n        }\n\n        .message-content a {\n            color: #60a5fa;\n            text-decoration: none;\n            font-weight: 500;\n            transition: all 0.2s ease;\n            border-bottom: 1px solid transparent;\n        }\n\n        .message-content a:hover {\n            color: #93c5fd;\n            border-bottom-color: #93c5fd;\n        }\n\n        .message-content table {\n            width: 100%;\n            border-collapse: separate;\n            border-spacing: 0;\n            margin: 1rem 0;\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));\n            border-radius: 0.5rem;\n            overflow: hidden;\n        }\n\n        .message-content th {\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));\n            color: #ffffff;\n            font-weight: 600;\n            padding: 0.75rem 1rem;\n            text-align: left;\n            font-size: 0.875rem;\n        }\n\n        .message-content td {\n            padding: 0.75rem 1rem;\n            border-bottom: 1px solid rgba(59, 130, 246, 0.2);\n            color: #cbd5e1;\n        }\n\n        .message-content tr:last-child td {\n            border-bottom: none;\n        }\n\n        .message-content hr {\n            margin: 1.5rem 0;\n            border: none;\n            height: 2px;\n            background: linear-gradient(90deg, transparent, #3b82f6, #60a5fa, #3b82f6, transparent);\n            border-radius: 1px;\n        }\n\n        .message-content strong {\n            color: #f1f5f9;\n            font-weight: 700;\n        }\n\n        .message-content em {\n            color: #cbd5e1;\n            font-style: italic;\n        }\n\n        /* ===== ESTILOS PARA KATEX ===== */\n        .message-content .katex {\n            font-size: 1.1em !important;\n            color: #e2e8f0 !important;\n        }\n\n        .message-content .katex-display {\n            margin: 1.5rem 0 !important;\n            padding: 1rem !important;\n            background: rgba(30, 58, 138, 0.1) !important;\n            border: 1px solid rgba(59, 130, 246, 0.3) !important;\n            border-radius: 0.5rem !important;\n            overflow-x: auto !important;\n            text-align: center;\n        }\n\n        .message-content .katex .base {\n            color: #f1f5f9 !important;\n        }\n\n        .message-content .katex .mbin,\n        .message-content .katex .mrel {\n            color: #60a5fa !important;\n        }\n\n        .message-content .katex .mord {\n            color: #e2e8f0 !important;\n        }\n\n        .message-content .katex .mop {\n            color: #34d399 !important;\n        }\n\n        .message-content .katex .mopen,\n        .message-content .katex .mclose {\n            color: #fbbf24 !important;\n        }\n\n        /* ===== ESTILOS PARA HIGHLIGHT.JS ===== */\n        .message-content .hljs {\n            background: transparent !important;\n            color: #e2e8f0;\n            padding: 0;\n        }\n\n        .message-content .hljs-keyword {\n            color: #c084fc !important;\n            font-weight: 600;\n        }\n\n        .message-content .hljs-string {\n            color: #34d399 !important;\n        }\n\n        .message-content .hljs-number {\n            color: #fbbf24 !important;\n        }\n\n        .message-content .hljs-comment {\n            color: #6b7280 !important;\n            font-style: italic;\n        }\n\n        .message-content .hljs-function {\n            color: #60a5fa !important;\n        }\n\n        .message-content .hljs-variable {\n            color: #f87171 !important;\n        }\n\n        .message-content .hljs-title {\n            color: #fbbf24 !important;\n            font-weight: 600;\n        }\n\n        .message-content .hljs-attr {\n            color: #60a5fa !important;\n        }\n\n        .message-content .hljs-built_in {\n            color: #c084fc !important;\n        }\n\n        .message-content .hljs-type {\n            color: #34d399 !important;\n        }\n\n        .message-content .hljs-literal {\n            color: #f87171 !important;\n        }\n\n        .message-content .hljs-meta {\n            color: #6b7280 !important;\n        }\n\n        .message-content .hljs-tag {\n            color: #60a5fa !important;\n        }\n\n        .message-content .hljs-name {\n            color: #fbbf24 !important;\n        }\n\n        /* ===== RESPONSIVIDADE ===== */\n        /* ===== RESPONSIVIDADE MELHORADA ===== */\n        @media (max-width: 768px) {\n            .container {\n                padding: 1.5rem 1rem;\n            }\n\n            .header {\n                padding: 2rem 1.5rem;\n                margin-bottom: 3rem;\n            }\n\n            .header h1 {\n                font-size: 2.25rem;\n            }\n\n            .header p {\n                font-size: 1rem;\n            }\n\n            .message {\n                margin-left: 1rem !important;\n                margin-right: 1rem !important;\n                padding: 1.5rem;\n            }\n\n            .message-header .icon {\n                width: 32px;\n                height: 32px;\n                font-size: 1rem;\n            }\n\n            .message-content {\n                padding: 1.5rem;\n                font-size: 0.9rem;\n            }\n\n            .message-content h1 { font-size: 1.5rem; }\n            .message-content h2 { font-size: 1.25rem; }\n            .message-content h3 { font-size: 1.125rem; }\n\n            .message-content pre {\n                padding: 1rem;\n                font-size: 0.8rem;\n            }\n\n            .message-content .katex-display {\n                font-size: 0.9em !important;\n                padding: 0.75rem !important;\n            }\n\n            .message-time {\n                font-size: 0.75rem;\n                padding: 0.375rem 0.75rem;\n            }\n\n            .footer {\n                padding: 2rem 1.5rem;\n                margin-top: 3rem;\n            }\n        }\n\n        @media (max-width: 480px) {\n            .header h1 {\n                font-size: 1.875rem;\n            }\n\n            .message {\n                margin-left: 0.5rem !important;\n                margin-right: 0.5rem !important;\n                padding: 1.25rem;\n            }\n\n            .message-content {\n                padding: 1.25rem;\n                font-size: 0.875rem;\n            }\n\n            .message-content h1 { font-size: 1.25rem; }\n            .message-content h2 { font-size: 1.125rem; }\n            .message-content h3 { font-size: 1rem; }\n        }\n\n        /* ===== EFEITOS ESPECIAIS ===== */\n        @keyframes fadeIn {\n            from { opacity: 0; transform: translateY(20px); }\n            to { opacity: 1; transform: translateY(0); }\n        }\n\n        .message {\n            animation: fadeIn 0.6s ease-out;\n        }\n\n        .message:nth-child(even) {\n            animation-delay: 0.1s;\n        }\n\n        .message:nth-child(odd) {\n            animation-delay: 0.2s;\n        }\n\n        /* ===== SCROLL SUAVE ===== */\n        html {\n            scroll-behavior: smooth;\n        }\n\n        /* ===== SELE\xc7\xc3O DE TEXTO ===== */\n        ::selection {\n            background: rgba(59, 130, 246, 0.3);\n            color: #ffffff;\n        }\n\n        ::-moz-selection {\n            background: rgba(59, 130, 246, 0.3);\n            color: #ffffff;\n        }\n\n        .message-time {\n            font-size: 0.875rem;\n            color: rgba(148, 163, 184, 0.8);\n            margin-top: 1.5rem;\n            text-align: right;\n            font-weight: 500;\n            padding: 0.5rem 1rem;\n            background: rgba(15, 23, 42, 0.3);\n            border-radius: 0.75rem;\n            border: 1px solid rgba(59, 130, 246, 0.1);\n            backdrop-filter: blur(5px);\n            display: inline-block;\n            float: right;\n            clear: both;\n        }\n\n        .footer {\n            text-align: center;\n            margin-top: 4rem;\n            padding: 3rem 2rem;\n            background: linear-gradient(135deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.1));\n            border-radius: 2rem;\n            border: 1px solid rgba(59, 130, 246, 0.3);\n            backdrop-filter: blur(20px);\n            box-shadow:\n                0 10px 25px -5px rgba(0, 0, 0, 0.3),\n                0 0 0 1px rgba(255, 255, 255, 0.05);\n            position: relative;\n            overflow: hidden;\n        }\n\n        .footer::before {\n            content: \'\';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            height: 1px;\n            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);\n        }\n\n        .footer p {\n            color: rgba(203, 213, 225, 0.9);\n            font-size: 1rem;\n            font-weight: 500;\n            margin: 0;\n            background: linear-gradient(135deg, #94a3b8, #cbd5e1);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n        }\n        \n        @media (max-width: 768px) {\n            .container {\n                padding: 1rem;\n            }\n            \n            .message.user {\n                margin-left: 0;\n            }\n            \n            .message.ai {\n                margin-right: 0;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class="container">\n        <div class="header">\n            <h1>').concat(t,"</h1>\n            <p>").concat(r[a]," • ").concat(e.length," mensagem").concat(1!==e.length?"s":"",'</p>\n        </div>\n        \n        <div class="messages">\n            ').concat(e.map(e=>'\n                <div class="message '.concat("user"===e.role?"user":"ai",'">\n                    <div class="message-header">\n                        <div class="icon">\n                            ').concat("user"===e.role?"\uD83D\uDC64":"\uD83E\uDD16","\n                        </div>\n                        ").concat("user"===e.role?"Voc\xea":"IA",'\n                    </div>\n                    <div class="message-content">').concat(processMessageContent(e.content),'</div>\n                    <div class="message-time">\n                        ').concat(new Date(e.timestamp).toLocaleString("pt-BR"),"\n                    </div>\n                </div>\n            ")).join(""),'\n        </div>\n        \n        <div class="footer">\n            <p>✨ Exportado em ').concat(new Date().toLocaleString("pt-BR")," • Rafthor AI ✨</p>\n        </div>\n    </div>\n\n    <script>\n        // Inicializar Highlight.js\n        document.addEventListener('DOMContentLoaded', function() {\n            hljs.highlightAll();\n\n            // Inicializar KaTeX\n            if (typeof renderMathInElement !== 'undefined') {\n                renderMathInElement(document.body, {\n                    delimiters: [\n                        {left: '$$', right: '$$', display: true},\n                        {left: '$', right: '$', display: false},\n                        {left: '\\\\(', right: '\\\\)', display: false},\n                        {left: '\\\\[', right: '\\\\]', display: true}\n                    ],\n                    throwOnError: false,\n                    errorColor: '#cc0000',\n                    strict: false,\n                    trust: false,\n                    macros: {\n                        \"\\\\f\": \"#1f(#2)\"\n                    }\n                });\n            }\n        });\n    </script>\n</body>\n</html>")},handleDownload=async()=>{c(!0);try{let e=n;"user"===l?e=n.filter(e=>"user"===e.role):"ai"===l&&(e=n.filter(e=>"assistant"===e.role));let t=generateStyledHTML(e,o,l),r=new Blob([t],{type:"text/html"}),s=URL.createObjectURL(r),i=document.createElement("a");i.href=s,i.download="".concat(o,"_").concat(l,"_messages.html"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(s),a()}catch(e){console.error("Error generating download:",e)}finally{c(!1)}};if(!m||!t)return null;let h=(0,r.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300",style:{zIndex:99999,position:"fixed",top:0,left:0,right:0,bottom:0},children:(0,r.jsxs)("div",{className:"border border-blue-600/30 rounded-2xl shadow-2xl p-0 w-full max-w-lg transform transition-all duration-300 relative",style:{background:"linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(29, 78, 216, 0.95) 50%, rgba(30, 58, 138, 0.95) 100%)",backdropFilter:"blur(20px)",position:"relative",zIndex:1e5},children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 pointer-events-none rounded-2xl"}),(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-blue-600/30 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-blue-100",children:"Download do Chat"}),(0,r.jsx)("p",{className:"text-sm text-blue-300/70",children:"Exporte suas conversas em HTML"})]})]}),(0,r.jsx)("button",{onClick:a,className:"text-blue-300 hover:text-blue-100 transition-all duration-200 p-2 rounded-xl hover:bg-blue-800/40 hover:scale-105",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"p-6 relative z-10",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("p",{className:"text-blue-200 font-medium mb-2",children:"Escolha quais mensagens incluir no arquivo HTML:"}),(0,r.jsx)("p",{className:"text-blue-300/70 text-sm",children:"O arquivo ser\xe1 exportado com formata\xe7\xe3o completa, incluindo Markdown, LaTeX, syntax highlighting e estilos profissionais"})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-8",children:[(0,r.jsx)("label",{className:"flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ".concat("all"===l?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"downloadType",value:"all",checked:"all"===l,onChange:e=>i(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),(0,r.jsx)("div",{className:"text-blue-100 font-semibold",children:"Todas as mensagens"})]}),(0,r.jsx)("div",{className:"text-blue-300/70 text-sm mt-1",children:"Inclui mensagens do usu\xe1rio e da IA"})]})]})}),(0,r.jsx)("label",{className:"flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ".concat("user"===l?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"downloadType",value:"user",checked:"user"===l,onChange:e=>i(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),(0,r.jsx)("div",{className:"text-blue-100 font-semibold",children:"Apenas mensagens do usu\xe1rio"})]}),(0,r.jsx)("div",{className:"text-blue-300/70 text-sm mt-1",children:"Somente suas perguntas e coment\xe1rios"})]})]})}),(0,r.jsx)("label",{className:"flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ".concat("ai"===l?"border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20":"border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"downloadType",value:"ai",checked:"ai"===l,onChange:e=>i(e.target.value),className:"w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),(0,r.jsx)("div",{className:"text-blue-100 font-semibold",children:"Apenas mensagens da IA"})]}),(0,r.jsx)("div",{className:"text-blue-300/70 text-sm mt-1",children:"Somente as respostas da intelig\xeancia artificial"})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)("button",{onClick:a,className:"px-6 py-3 text-blue-300 hover:text-blue-100 transition-all duration-200 rounded-xl hover:bg-blue-800/30 font-medium",children:"Cancelar"}),(0,r.jsx)("button",{onClick:handleDownload,disabled:d,className:"px-8 py-3 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold shadow-lg hover:shadow-blue-500/30 hover:scale-105 disabled:hover:scale-100",children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"}),"Gerando arquivo..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Baixar HTML"]})})]})]})]})});return(0,S.createPortal)(h,document.body)};let getUserAPIEndpoints=async e=>{try{let t=(0,i.collection)(l.db,"usuarios",e,"endpoints"),a=await (0,i.getDocs)(t);if(!a.empty){let e=[];return a.forEach(t=>{e.push(t.data())}),e.sort((e,t)=>t.createdAt-e.createdAt),e}let r=(0,i.doc)(l.db,"usuarios",e,"configuracoes","settings"),s=await (0,i.getDoc)(r);if(s.exists()){let e=s.data();if(e.endpoints){let t=[];return Object.entries(e.endpoints).forEach(e=>{let[a,r]=e;t.push({id:"legacy_".concat(a),name:r.nome||a,url:r.url||"",apiKey:r.apiKey||"",isActive:r.ativo||!1,createdAt:Date.now(),settings:{defaultModel:r.modeloPadrao}})}),t}}return[{id:"default_openrouter",name:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",isActive:!1,createdAt:Date.now(),settings:{defaultModel:"meta-llama/llama-3.1-8b-instruct:free"}},{id:"default_deepseek",name:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",isActive:!1,createdAt:Date.now(),settings:{defaultModel:"deepseek-chat"}}]}catch(e){throw console.error("Error getting user API endpoints:",e),e}},D=new class{async fetchModels(){if(this.cache&&Date.now()-this.cache.timestamp<this.CACHE_DURATION)return this.cache.models;try{let e=await fetch("https://openrouter.ai/api/v1/models",{headers:{Authorization:"Bearer ".concat("your-openrouter-api-key-here"),"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json(),a=t.data.map(e=>({id:e.id,name:e.name,description:e.description||"",context_length:e.context_length,pricing:{prompt:e.pricing.prompt,completion:e.pricing.completion,image:e.pricing.image},architecture:e.architecture,created:e.created,isFavorite:!1}));return this.cache={models:a,timestamp:Date.now()},a}catch(e){if(console.error("Error fetching OpenRouter models:",e),this.cache)return this.cache.models;throw e}}async fetchCredits(e){if(this.creditsCache&&Date.now()-this.creditsCache.timestamp<this.CREDITS_CACHE_DURATION){let e=this.creditsCache.credits.total_credits-this.creditsCache.credits.total_usage;return{balance:e}}try{let t=await fetch("https://openrouter.ai/api/v1/credits",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let a=await t.json(),r=a.data;this.creditsCache={credits:r,timestamp:Date.now()};let s=r.total_credits-r.total_usage;return{balance:s}}catch(e){if(console.error("Error fetching OpenRouter credits:",e),this.creditsCache){let e=this.creditsCache.credits.total_credits-this.creditsCache.credits.total_usage;return{balance:e}}return{balance:0,error:e instanceof Error?e.message:"Erro desconhecido"}}}filterByCategory(e,t){switch(t){case"free":return e.filter(e=>this.isFreeModel(e));case"paid":return e.filter(e=>!this.isFreeModel(e));case"favorites":return e.filter(e=>e.isFavorite);default:return e}}sortModels(e,t){let a=[...e];switch(t){case"newest":return a.sort((e,t)=>(t.created||0)-(e.created||0));case"price_low":return a.sort((e,t)=>this.getTotalPrice(e)-this.getTotalPrice(t));case"price_high":return a.sort((e,t)=>this.getTotalPrice(t)-this.getTotalPrice(e));case"context_high":return a.sort((e,t)=>t.context_length-e.context_length);default:return a}}isFreeModel(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}getTotalPrice(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}formatPrice(e){let t=1e6*parseFloat(e);return 0===t?"Gr\xe1tis":t<.001?"< $0.001":"$".concat(t.toFixed(3))}formatContextLength(e){return e.toLocaleString()}searchModels(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:n=!1}=a;if(!t.trim())return[];let o=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(o)&&(e+=10,a.push("name"),r=this.highlightText(t.name,o)),t.id.toLowerCase().includes(o)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(o)&&(e+=3,a.push("description"),s=this.highlightText(t.description,o)),n&&t.isFavorite&&(e*=1.5),(o.includes("free")||o.includes("gr\xe1tis"))&&this.isFreeModel(t)&&(e+=5),(o.includes("expensive")||o.includes("caro"))&&this.getTotalPrice(t)>2e-5&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}highlightText(e,t){let a=RegExp("(".concat(t,")"),"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}clearCache(){this.cache=null}constructor(){this.cache=null,this.creditsCache=null,this.CACHE_DURATION=3e5,this.CREDITS_CACHE_DURATION=3e4}},A=new class{async fetchModels(){if(this.cache&&Date.now()-this.cache.timestamp<this.CACHE_DURATION)return this.cache.models;try{let e=[{id:"deepseek-chat",name:"DeepSeek Chat",description:"Modelo de chat geral da DeepSeek, otimizado para conversas e tarefas diversas.",context_length:32768,pricing:{prompt:"0.00014",completion:"0.00028"},architecture:{input_modalities:["text"],output_modalities:["text"],tokenizer:"deepseek"},created:Date.now(),isFavorite:!1},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Modelo especializado em programa\xe7\xe3o e desenvolvimento de c\xf3digo.",context_length:16384,pricing:{prompt:"0.00014",completion:"0.00028"},architecture:{input_modalities:["text"],output_modalities:["text"],tokenizer:"deepseek"},created:Date.now(),isFavorite:!1}];return this.cache={models:e,timestamp:Date.now()},e}catch(e){if(console.error("Error fetching DeepSeek models:",e),this.cache)return this.cache.models;throw e}}filterByCategory(e,t){switch(t){case"free":return e.filter(e=>this.isFreeModel(e));case"paid":return e.filter(e=>!this.isFreeModel(e));default:return e}}sortModels(e,t){let a=[...e];switch(t){case"newest":return a.sort((e,t)=>(t.created||0)-(e.created||0));case"price_low":return a.sort((e,t)=>this.getTotalPrice(e)-this.getTotalPrice(t));case"price_high":return a.sort((e,t)=>this.getTotalPrice(t)-this.getTotalPrice(e));case"context_high":return a.sort((e,t)=>t.context_length-e.context_length);default:return a}}isFreeModel(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}getTotalPrice(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}formatPrice(e){let t=1e6*parseFloat(e);return 0===t?"Gr\xe1tis":t<.001?"< $0.001":"$".concat(t.toFixed(3))}formatContextLength(e){return e.toLocaleString()}searchModels(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:n=!1}=a;if(!t.trim())return[];let o=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(o)&&(e+=10,a.push("name"),r=this.highlightText(t.name,o)),t.id.toLowerCase().includes(o)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(o)&&(e+=3,a.push("description"),s=this.highlightText(t.description,o)),n&&t.isFavorite&&(e*=1.5),(o.includes("free")||o.includes("gr\xe1tis"))&&this.isFreeModel(t)&&(e+=5),(o.includes("code")||o.includes("programming")||o.includes("coder"))&&t.id.includes("coder")&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}highlightText(e,t){let a=RegExp("(".concat(t,")"),"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}clearCache(){this.cache=null}constructor(){this.cache=null,this.CACHE_DURATION=3e5}},I=new class{escapeModelId(e){return e.replace(/\//g,"_SLASH_").replace(/\./g,"_DOT_")}unescapeModelId(e){return e.replace(/_SLASH_/g,"/").replace(/_DOT_/g,".")}async addToFavorites(e,t,a,r){try{let s=this.escapeModelId(a),n=(0,i.doc)(l.db,"usuarios",e,"endpoints",t,"modelos_favoritos",s),o={modelId:a,modelName:r,endpointId:t,addedAt:Date.now()};await (0,i.pl)(n,o),console.log("Model added to favorites successfully")}catch(e){throw console.error("Error adding model to favorites:",e),e}}async removeFromFavorites(e,t,a){try{let r=this.escapeModelId(a),s=(0,i.doc)(l.db,"usuarios",e,"endpoints",t,"modelos_favoritos",r);await (0,i.oe)(s),console.log("Model removed from favorites successfully")}catch(e){throw console.error("Error removing model from favorites:",e),e}}async isModelFavorited(e,t,a){try{let r=this.escapeModelId(a),s=(0,i.doc)(l.db,"usuarios",e,"endpoints",t,"modelos_favoritos",r),n=await (0,i.getDoc)(s);return n.exists()}catch(e){return console.error("Error checking if model is favorited:",e),!1}}async toggleFavorite(e,t,a,r){try{let s=await this.isModelFavorited(e,t,a);if(s)return await this.removeFromFavorites(e,t,a),!1;return await this.addToFavorites(e,t,a,r),!0}catch(e){throw console.error("Error toggling favorite:",e),e}}async getFavoriteModels(e,t){try{let a=(0,i.collection)(l.db,"usuarios",e,"endpoints",t,"modelos_favoritos"),r=await (0,i.getDocs)(a),s=[];return r.forEach(e=>{let t=e.data();t.modelId=this.unescapeModelId(t.modelId),s.push(t)}),s.sort((e,t)=>t.addedAt-e.addedAt),s}catch(e){throw console.error("Error getting favorite models:",e),e}}async getFavoriteModelIds(e,t){try{let a=await this.getFavoriteModels(e,t);return new Set(a.map(e=>e.modelId))}catch(e){return console.error("Error getting favorite model IDs:",e),new Set}}async getFavoritesStats(e,t){try{let a=await this.getFavoriteModels(e,t);if(0===a.length)return{totalFavorites:0};let r=[...a].sort((e,t)=>t.addedAt-e.addedAt);return{totalFavorites:a.length,mostRecentlyAdded:r[0],oldestFavorite:r[r.length-1]}}catch(e){return console.error("Error getting favorites stats:",e),{totalFavorites:0}}}async clearAllFavorites(e,t){try{let a=await this.getFavoriteModels(e,t),r=a.map(a=>this.removeFromFavorites(e,t,a.modelId));await Promise.all(r),console.log("All favorites cleared successfully")}catch(e){throw console.error("Error clearing all favorites:",e),e}}async exportFavorites(e,t){try{return await this.getFavoriteModels(e,t)}catch(e){throw console.error("Error exporting favorites:",e),e}}async importFavorites(e,t,a){try{let r=a.map(a=>this.addToFavorites(e,t,a.modelId,a.modelName));await Promise.all(r),console.log("Favorites imported successfully")}catch(e){throw console.error("Error importing favorites:",e),e}}},T=new class{async trackSearchTerm(e,t){if(t.trim()&&e)try{let a=t.toLowerCase().trim();await this.updateGlobalSearchStats(a),await this.updateUserSearchHistory(e,a)}catch(e){console.error("Error tracking search term:",e)}}async trackModelSelection(e,t,a,r){if(e&&t)try{let s={modelId:t,modelName:a,searchTerm:(null==r?void 0:r.trim())||void 0,timestamp:Date.now(),userId:e},n=(0,i.doc)((0,i.collection)(l.db,"search_analytics","model_selections","events"));await (0,i.pl)(n,s),await this.updateModelStats(t,a)}catch(e){console.error("Error tracking model selection:",e)}}async updateGlobalSearchStats(e){try{let t=(0,i.doc)(l.db,"search_analytics","global","terms",e),a=await (0,i.getDoc)(t);a.exists()?await (0,i.r7)(t,{count:(0,i.nP)(1),lastUsed:Date.now()}):await (0,i.pl)(t,{term:e,count:1,firstUsed:Date.now(),lastUsed:Date.now()})}catch(e){console.error("Error updating global search stats:",e)}}async updateUserSearchHistory(e,t){try{let a=(0,i.doc)(l.db,"search_analytics","users",e),r=await (0,i.getDoc)(a);if(r.exists()){let e=r.data(),s=e.searches.find(e=>e.term===t);s?(s.count++,s.lastUsed=Date.now()):e.searches.push({term:t,count:1,firstUsed:Date.now(),lastUsed:Date.now()}),e.searches=e.searches.sort((e,t)=>t.lastUsed-e.lastUsed).slice(0,50),await (0,i.r7)(a,{searches:e.searches,totalSearches:(0,i.nP)(1),lastSearchAt:Date.now()})}else{let r={userId:e,searches:[{term:t,count:1,firstUsed:Date.now(),lastUsed:Date.now()}],totalSearches:1,lastSearchAt:Date.now()};await (0,i.pl)(a,r)}}catch(e){console.error("Error updating user search history:",e)}}async updateModelStats(e,t){try{let a=(0,i.doc)(l.db,"search_analytics","models",e),r=await (0,i.getDoc)(a);r.exists()?await (0,i.r7)(a,{selectionCount:(0,i.nP)(1),lastSelected:Date.now()}):await (0,i.pl)(a,{modelId:e,modelName:t,selectionCount:1,firstSelected:Date.now(),lastSelected:Date.now()})}catch(e){console.error("Error updating model stats:",e)}}async getPopularSearchTerms(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{let t=(0,i.collection)(l.db,"search_analytics","global","terms"),a=(0,i.query)(t,(0,i.Xo)("count","desc"),(0,i.b9)(e)),r=await (0,i.getDocs)(a),s=[];return r.forEach(e=>{s.push(e.data())}),s}catch(e){return console.error("Error getting popular search terms:",e),[]}}async getUserSearchHistory(e){try{let t=(0,i.doc)(l.db,"search_analytics","users",e),a=await (0,i.getDoc)(t);if(a.exists()){let e=a.data();return e.searches.sort((e,t)=>t.lastUsed-e.lastUsed)}return[]}catch(e){return console.error("Error getting user search history:",e),[]}}async getSearchSuggestions(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5;try{let r=await this.getUserSearchHistory(e),s=await this.getPopularSearchTerms(20),n=[...r.map(e=>e.term),...s.map(e=>e.term)],o=n.filter(e=>e.toLowerCase().startsWith(t.toLowerCase())&&e.toLowerCase()!==t.toLowerCase()).slice(0,a);return Array.from(new Set(o))}catch(e){return console.error("Error getting search suggestions:",e),[]}}async getUserAnalytics(e){try{let t=await this.getUserSearchHistory(e);if(0===t.length)return{totalSearches:0,uniqueTerms:0,recentSearches:[]};let a=t.reduce((e,t)=>e+t.count,0),r=t.reduce((e,t)=>t.count>e.count?t:e);return{totalSearches:a,uniqueTerms:t.length,mostUsedTerm:r,recentSearches:t.slice(0,10),lastSearchAt:Math.max(...t.map(e=>e.lastUsed))}}catch(e){return console.error("Error getting user analytics:",e),{totalSearches:0,uniqueTerms:0,recentSearches:[]}}}async clearUserSearchHistory(e){try{let t=(0,i.doc)(l.db,"search_analytics","users",e);await (0,i.pl)(t,{userId:e,searches:[],totalSearches:0,lastSearchAt:Date.now()})}catch(e){throw console.error("Error clearing user search history:",e),e}}};function highlightText(e,t){let a=RegExp("(".concat(t,")"),"gi");return e.replace(a,'<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>')}let F=new class{getSmartCategories(){return this.smartCategories}getModelsByCategory(e,t){let a=this.smartCategories.find(e=>e.id===t);return a?e.filter(a.filter):e}getCategoryStats(e){let t=e.length;return this.smartCategories.map(a=>{let r=e.filter(a.filter);return{category:a,count:r.length,percentage:t>0?r.length/t*100:0}})}filterByPriceRange(e,t,a){return e.filter(e=>{let r=parseFloat(e.pricing.prompt),s=parseFloat(e.pricing.completion),n=r+s;return n>=t&&n<=a})}filterByContextRange(e,t,a){return e.filter(e=>e.context_length>=t&&e.context_length<=a)}filterByInputModalities(e,t){return e.filter(e=>{var a;return null!==(a=e.architecture)&&void 0!==a&&!!a.input_modalities&&t.every(t=>e.architecture.input_modalities.includes(t))})}filterByKeywords(e,t){return e.filter(e=>{let a="".concat(e.name," ").concat(e.description||""," ").concat(e.id).toLowerCase();return t.some(e=>a.includes(e.toLowerCase()))})}applyAdvancedFilters(e,t){let a=[...e];return t.categories&&t.categories.length>0&&(a=a.filter(e=>t.categories.some(t=>{let a=this.smartCategories.find(e=>e.id===t);return!!a&&a.filter(e)}))),t.priceRange&&(a=this.filterByPriceRange(a,t.priceRange.min,t.priceRange.max)),t.contextRange&&(a=this.filterByContextRange(a,t.contextRange.min,t.contextRange.max)),t.inputModalities&&t.inputModalities.length>0&&(a=this.filterByInputModalities(a,t.inputModalities)),t.keywords&&t.keywords.length>0&&(a=this.filterByKeywords(a,t.keywords)),t.onlyFavorites&&(a=a.filter(e=>e.isFavorite)),a}getSuggestedFilters(e){let t=this.getCategoryStats(e),a=t.filter(e=>e.count>0).sort((e,t)=>t.count-e.count).slice(0,5),r=[{label:"Gratuito",min:0,max:0},{label:"Muito Barato (< $1/1M)",min:1e-9,max:1e-6},{label:"Barato ($1-10/1M)",min:1e-6,max:1e-5},{label:"M\xe9dio ($10-100/1M)",min:1e-5,max:1e-4},{label:"Caro (> $100/1M)",min:1e-4,max:1/0}].map(t=>({...t,count:this.filterByPriceRange(e,t.min,t.max).length})),s=[{label:"Pequeno (< 8K)",min:0,max:8e3},{label:"M\xe9dio (8K - 32K)",min:8e3,max:32e3},{label:"Grande (32K - 128K)",min:32e3,max:128e3},{label:"Muito Grande (> 128K)",min:128e3,max:1/0}].map(t=>({...t,count:this.filterByContextRange(e,t.min,t.max).length}));return{popularCategories:a,priceRanges:r,contextRanges:s}}constructor(){this.smartCategories=[{id:"vision",name:"Vis\xe3o",description:"Modelos que processam imagens",icon:"\uD83D\uDC41️",filter:e=>{var t,a,r,s;return!!((null===(a=e.architecture)||void 0===a?void 0:null===(t=a.input_modalities)||void 0===t?void 0:t.includes("image"))||e.name.toLowerCase().includes("vision")||(null===(r=e.description)||void 0===r?void 0:r.toLowerCase().includes("vision"))||(null===(s=e.description)||void 0===s?void 0:s.toLowerCase().includes("image")))}},{id:"coding",name:"C\xf3digo",description:"Modelos especializados em programa\xe7\xe3o",icon:"\uD83D\uDCBB",filter:e=>["code","coding","programming","developer","coder"].some(t=>{var a;return e.name.toLowerCase().includes(t)||!!(null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(t))})},{id:"reasoning",name:"Racioc\xednio",description:"Modelos otimizados para racioc\xednio l\xf3gico",icon:"\uD83E\uDDE0",filter:e=>["reasoning","logic","math","analysis","thinking"].some(t=>{var a;return e.name.toLowerCase().includes(t)||!!(null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(t))})},{id:"creative",name:"Criativo",description:"Modelos para tarefas criativas",icon:"\uD83C\uDFA8",filter:e=>["creative","writing","story","art","creative"].some(t=>{var a;return e.name.toLowerCase().includes(t)||!!(null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(t))})},{id:"fast",name:"R\xe1pido",description:"Modelos otimizados para velocidade",icon:"⚡",filter:e=>["fast","quick","speed","turbo","instant"].some(t=>{var a;return e.name.toLowerCase().includes(t)||!!(null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(t))})},{id:"large_context",name:"Grande Contexto",description:"Modelos com contexto extenso (>32K)",icon:"\uD83D\uDCDA",filter:e=>e.context_length>32e3},{id:"cheap",name:"Econ\xf4mico",description:"Modelos com pre\xe7os baixos",icon:"\uD83D\uDCB0",filter:e=>{let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a<1e-6}},{id:"premium",name:"Premium",description:"Modelos de alta qualidade",icon:"⭐",filter:e=>["gpt-4","claude-3","premium","pro","advanced"].some(t=>e.name.toLowerCase().includes(t)||e.id.toLowerCase().includes(t))}]}},HighlightedText=e=>{let{text:t,highlight:a}=e;if(!a.trim())return(0,r.jsx)("span",{children:t});let s=RegExp("(".concat(a,")"),"gi"),n=t.split(s);return(0,r.jsx)("span",{children:n.map((e,t)=>s.test(e)?(0,r.jsx)("mark",{className:"bg-yellow-300 text-black px-1 rounded",children:e},t):(0,r.jsx)("span",{children:e},t))})};var components_AdvancedSearchInput=e=>{let{value:t,onChange:a,suggestions:n=[],isSearching:o=!1,placeholder:l="Buscar...",showSuggestions:i=!0,onSuggestionSelect:d,className:c=""}=e,[m,u]=(0,s.useState)(!1),[h,x]=(0,s.useState)(-1),p=(0,s.useRef)(null),g=(0,s.useRef)(null);(0,s.useEffect)(()=>{i&&n.length>0&&t.trim()?u(!0):u(!1)},[n,t,i]),(0,s.useEffect)(()=>{let handleClickOutside=e=>{p.current&&g.current&&!p.current.contains(e.target)&&!g.current.contains(e.target)&&(u(!1),x(-1))};return document.addEventListener("mousedown",handleClickOutside),()=>{document.removeEventListener("mousedown",handleClickOutside)}},[]);let handleSuggestionClick=e=>{var t;a(e),null==d||d(e),u(!1),x(-1),null===(t=p.current)||void 0===t||t.focus()};return(0,r.jsxs)("div",{className:"relative ".concat(c),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-400"}):(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,r.jsx)("input",{ref:p,type:"text",value:t,onChange:e=>{let t=e.target.value;a(t),x(-1)},onKeyDown:e=>{if(m&&0!==n.length)switch(e.key){case"ArrowDown":e.preventDefault(),x(e=>e<n.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),x(e=>e>0?e-1:n.length-1);break;case"Enter":if(e.preventDefault(),h>=0){let e=n[h];a(e),null==d||d(e),u(!1),x(-1)}break;case"Escape":var t;u(!1),x(-1),null===(t=p.current)||void 0===t||t.blur()}},onFocus:()=>{i&&n.length>0&&t.trim()&&u(!0)},placeholder:l,className:"w-full pl-10 pr-10 py-3 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"}),t&&(0,r.jsx)("button",{onClick:()=>{var e;a(""),u(!1),x(-1),null===(e=p.current)||void 0===e||e.focus()},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-blue-300 hover:text-blue-200 transition-colors duration-200",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),m&&n.length>0&&(0,r.jsx)("div",{ref:g,className:"absolute z-50 w-full mt-1 bg-blue-900/95 backdrop-blur-sm border border-blue-600/30 rounded-xl shadow-2xl max-h-60 overflow-y-auto",children:n.map((e,t)=>(0,r.jsx)("button",{onClick:()=>handleSuggestionClick(e),className:"w-full text-left px-4 py-3 text-sm transition-all duration-200 first:rounded-t-xl last:rounded-b-xl ".concat(t===h?"bg-blue-600/50 text-blue-100":"text-blue-200 hover:bg-blue-800/50 hover:text-blue-100"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-3 h-3 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,r.jsx)("span",{children:e})]})},e))})]})},dashboard_ExpensiveModelConfirmationModal=e=>{let{isOpen:t,model:a,onConfirm:s,onCancel:n}=e;if(!t||!a)return null;let o=parseFloat(a.pricing.prompt),l=parseFloat(a.pricing.completion),i=o+l,formatPrice=e=>{let t=1e6*e;return 0===t?"Gr\xe1tis":t<.001?"< $0.001":"$".concat(t.toFixed(3))};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300 z-[60]",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-amber-950/95 via-orange-900/95 to-red-950/95 backdrop-blur-xl rounded-2xl border border-amber-600/40 shadow-2xl w-full max-w-md mx-4 overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-amber-500/10 via-transparent to-red-500/10 pointer-events-none rounded-2xl"}),(0,r.jsx)("div",{className:"p-6 border-b border-amber-700/30 relative z-10",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-amber-100",children:"Modelo Caro Detectado"}),(0,r.jsx)("p",{className:"text-amber-200/70 text-sm mt-1",children:"Este modelo tem custos elevados"})]})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6 relative z-10",children:[(0,r.jsxs)("div",{className:"bg-amber-900/30 backdrop-blur-sm rounded-xl p-4 border border-amber-600/30",children:[(0,r.jsx)("h3",{className:"font-semibold text-amber-100 mb-2",children:a.name}),(0,r.jsx)("p",{className:"text-amber-200/80 text-sm mb-3",children:a.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{className:"bg-amber-800/30 rounded-lg p-3 border border-amber-600/20",children:[(0,r.jsx)("span",{className:"block text-xs text-amber-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("span",{className:"text-amber-200 font-semibold",children:[formatPrice(o),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-amber-800/30 rounded-lg p-3 border border-amber-600/20",children:[(0,r.jsx)("span",{className:"block text-xs text-amber-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("span",{className:"text-amber-200 font-semibold",children:[formatPrice(l),"/1M"]})]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-gradient-to-r from-red-900/40 to-orange-900/40 rounded-lg border border-red-600/30",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-red-200 font-medium",children:"Custo Total por 1M tokens:"}),(0,r.jsx)("span",{className:"text-red-100 font-bold text-lg",children:formatPrice(i)})]})})]}),(0,r.jsx)("div",{className:"bg-red-900/30 backdrop-blur-sm rounded-xl p-4 border border-red-600/30",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-red-200 font-medium mb-2",children:"⚠️ Aten\xe7\xe3o aos Custos"}),(0,r.jsxs)("ul",{className:"text-red-300/80 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Este modelo tem custos superiores a $20 por milh\xe3o de tokens"}),(0,r.jsx)("li",{children:"• Conversas longas podem gerar custos significativos"}),(0,r.jsx)("li",{children:"• Monitore seu uso para evitar surpresas na fatura"})]})]})]})}),(0,r.jsxs)("div",{className:"bg-blue-900/30 backdrop-blur-sm rounded-xl p-4 border border-blue-600/30",children:[(0,r.jsx)("h4",{className:"text-blue-200 font-medium mb-3",children:"\uD83D\uDCA1 Estimativa de Custos"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[(0,r.jsx)("span",{children:"Mensagem curta (~100 tokens):"}),(0,r.jsx)("span",{className:"font-medium",children:formatPrice(1e-4*i)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[(0,r.jsx)("span",{children:"Mensagem m\xe9dia (~500 tokens):"}),(0,r.jsx)("span",{className:"font-medium",children:formatPrice(5e-4*i)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-blue-300/80",children:[(0,r.jsx)("span",{children:"Mensagem longa (~2000 tokens):"}),(0,r.jsx)("span",{className:"font-medium",children:formatPrice(.002*i)})]})]})]})]}),(0,r.jsxs)("div",{className:"p-6 border-t border-amber-700/30 flex space-x-3 relative z-10",children:[(0,r.jsx)("button",{onClick:n,className:"flex-1 px-6 py-3 bg-gray-700/50 hover:bg-gray-600/50 backdrop-blur-sm border border-gray-600/30 hover:border-gray-500/50 rounded-xl text-gray-200 hover:text-gray-100 transition-all duration-200 font-medium",children:"Cancelar"}),(0,r.jsx)("button",{onClick:s,className:"flex-1 px-6 py-3 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-500 hover:to-orange-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-amber-500/30",children:"Usar Mesmo Assim"})]})]})})};let ModelCard=e=>{let{model:t,isSelected:a,onSelect:s,onToggleFavorite:n,isToggling:o=!1,service:l=D,searchTerm:i="",searchResult:d}=e,c=l===D&&D.getTotalPrice(t)>2e-5;return(0,r.jsxs)("div",{className:"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative ".concat(a?"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20":"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40"),children:[c&&(0,r.jsx)("div",{className:"absolute -top-2 -right-2 z-10",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})})}),(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-100 truncate",children:(null==d?void 0:d.highlightedName)?(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:d.highlightedName}}):i?(0,r.jsx)(HighlightedText,{text:t.name,highlight:i}):t.name}),c&&(0,r.jsx)("span",{className:"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium",children:"CARO"}),d&&d.matchedFields.length>0&&(0,r.jsx)("div",{className:"flex space-x-1",children:d.matchedFields.slice(0,3).map(e=>(0,r.jsx)("span",{className:"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30",title:"Encontrado em: ".concat(e),children:"name"===e?"\uD83D\uDCDD":"description"===e?"\uD83D\uDCC4":"provider"===e?"\uD83C\uDFE2":"tags"===e?"\uD83C\uDFF7️":"\uD83D\uDD0D"},e))})]}),(0,r.jsx)("p",{className:"text-xs text-blue-300/70 truncate mt-1 font-mono",children:(0,r.jsx)(HighlightedText,{text:t.id,highlight:i})})]}),l.isFreeModel(t)&&(0,r.jsx)("span",{className:"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium",children:"Gr\xe1tis"})]}),t.description&&(0,r.jsx)("div",{className:"mt-3 mb-3",children:(0,r.jsx)("p",{className:"text-sm text-blue-300/80 line-clamp-2",children:(null==d?void 0:d.highlightedDescription)?(0,r.jsx)("span",{dangerouslySetInnerHTML:{__html:d.highlightedDescription}}):i?(0,r.jsx)(HighlightedText,{text:t.description,highlight:i}):t.description})}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[(0,r.jsx)("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Contexto"}),(0,r.jsx)("span",{className:"text-blue-200 font-semibold",children:l.formatContextLength(t.context_length)})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[(0,r.jsx)("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("span",{className:"text-blue-200 font-semibold",children:[l.formatPrice(t.pricing.prompt),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20",children:[(0,r.jsx)("span",{className:"block text-xs text-blue-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("span",{className:"text-blue-200 font-semibold",children:[l.formatPrice(t.pricing.completion),"/1M"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("button",{onClick:n,disabled:o,className:"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ".concat(t.isFavorite?"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30":"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30"),title:o?"Processando...":t.isFavorite?"Remover dos favoritos":"Adicionar aos favoritos",children:o?(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current"}):(0,r.jsx)("svg",{className:"w-5 h-5",fill:t.isFavorite?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})})}),(0,r.jsx)("button",{onClick:s,className:"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30",children:"Selecionar"})]})]})]})},DeepSeekModelCard=e=>{let{model:t,isSelected:a,onSelect:s}=e;return(0,r.jsxs)("div",{className:"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] ".concat(a?"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20":"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40"),onClick:s,children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:"deepseek-chat"===t.id?(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}):(0,r.jsx)("svg",{className:"w-8 h-8 text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-100 text-lg",children:t.name}),(0,r.jsx)("p",{className:"text-sm text-blue-300/70 mt-1",children:t.description})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[(0,r.jsx)("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Contexto"}),(0,r.jsx)("div",{className:"text-sm font-semibold text-blue-200",children:A.formatContextLength(t.context_length)})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[(0,r.jsx)("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Input"}),(0,r.jsxs)("div",{className:"text-sm font-semibold text-blue-200",children:[A.formatPrice(t.pricing.prompt),"/1M"]})]}),(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center",children:[(0,r.jsx)("div",{className:"text-xs text-blue-300/70 font-medium mb-1",children:"Output"}),(0,r.jsxs)("div",{className:"text-sm font-semibold text-blue-200",children:[A.formatPrice(t.pricing.completion),"/1M"]})]})]}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s()},className:"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 ".concat(a?"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30":"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30"),children:a?"Selecionado":"Selecionar Modelo"})]})]})};var dashboard_ModelSelectionModal=e=>{let{isOpen:t,onClose:o,currentModel:i,onModelSelect:d}=e,{user:c}=(0,n.useAuth)(),[m,u]=(0,s.useState)([]),[h,x]=(0,s.useState)(null),[p,g]=(0,s.useState)([]),[b,f]=(0,s.useState)(new Set),[v,j]=(0,s.useState)(!1),[w,y]=(0,s.useState)(null),[N,k]=(0,s.useState)(4),[C,M]=(0,s.useState)(""),[S,E]=(0,s.useState)(!1),[L,P]=(0,s.useState)(null),[z,B]=(0,s.useState)(null),[R,O]=(0,s.useState)([]),[W,U]=(0,s.useState)(new Set),[H,_]=(0,s.useState)(null),[V,q]=(0,s.useState)(new Map),[K,G]=(0,s.useState)(null),[Z,$]=(0,s.useState)({category:"paid",sortBy:"newest",searchTerm:""}),{searchTerm:X,setSearchTerm:J,searchResults:Y,suggestions:Q,isSearching:ee,hasSearched:et,clearSearch:ea,trackModelSelection:er}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{debounceMs:a=300,enableSuggestions:r=!0,cacheResults:n=!0,fuzzyThreshold:o=.6,maxResults:l=50,boostFavorites:i=!1,userId:d=null,trackAnalytics:c=!0}=t,[m,u]=(0,s.useState)(""),[h,x]=(0,s.useState)([]),[p,g]=(0,s.useState)([]),[b,f]=(0,s.useState)(!1),[v,j]=(0,s.useState)(!1),[w,y]=(0,s.useState)(null),[N]=(0,s.useState)(new Map),k=(0,s.useCallback)(async t=>{if(!t.trim()){x([]),j(!1);return}f(!0);try{if(n&&N.has(t)){x(N.get(t)),j(!0),f(!1);return}let a=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{fuzzyThreshold:r=.6,maxResults:s=50,boostFavorites:n=!1}=a;if(!t.trim())return[];let o=t.toLowerCase(),l=[];for(let t of e){let e=0,a=[],r=t.name,s=t.description||"";t.name.toLowerCase().includes(o)&&(e+=10,a.push("name"),r=highlightText(t.name,o)),t.id.toLowerCase().includes(o)&&(e+=7,a.push("id")),t.description&&t.description.toLowerCase().includes(o)&&(e+=3,a.push("description"),s=highlightText(t.description,o)),n&&t.isFavorite&&(e*=1.5),(o.includes("free")||o.includes("gr\xe1tis"))&&function(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return 0===t&&0===a}(t)&&(e+=5),(o.includes("expensive")||o.includes("caro"))&&function(e){let t=parseFloat(e.pricing.prompt),a=parseFloat(e.pricing.completion);return t+a}(t)>2e-5&&(e+=5),e>0&&l.push({model:t,score:e,matchedFields:a,highlightedName:r,highlightedDescription:s})}return l.sort((e,t)=>t.score-e.score).slice(0,s)}(e,t,{fuzzyThreshold:o,maxResults:l,boostFavorites:i});n&&N.set(t,a),x(a),j(!0),c&&d&&await T.trackSearchTerm(d,t)}catch(e){console.error("Error performing search:",e),x([])}finally{f(!1)}},[e,o,l,i,n,N,c,d]),C=(0,s.useCallback)(e=>{u(e),w&&clearTimeout(w);let t=setTimeout(()=>{k(e)},a);y(t)},[w,a,k]);(0,s.useEffect)(()=>{r&&m.length>0&&d?T.getSearchSuggestions(d,m,5).then(g).catch(e=>{console.error("Error loading suggestions:",e),g([])}):g([])},[m,r,d]);let M=(0,s.useCallback)(()=>{u(""),x([]),j(!1),g([]),w&&(clearTimeout(w),y(null))},[w]),S=(0,s.useCallback)(async t=>{if(c&&d){let a=e.find(e=>e.id===t);a&&await T.trackModelSelection(d,t,a.name,v?m:void 0)}},[c,d,e,v,m]);return(0,s.useEffect)(()=>()=>{w&&clearTimeout(w)},[w]),{searchTerm:m,setSearchTerm:C,searchResults:h,suggestions:p,isSearching:b,hasSearched:v,clearSearch:M,trackModelSelection:S}}(p,{debounceMs:300,enableSuggestions:!1,cacheResults:!0,fuzzyThreshold:.6,maxResults:50,boostFavorites:!0,userId:(null==c?void 0:c.email)||null,trackAnalytics:!0});(0,s.useEffect)(()=>{if(c&&t){if(K&&Date.now()-K.timestamp<6e5){if(console.log("Using cached endpoints"),u(K.endpoints),!h&&K.endpoints.length>0){let e=K.endpoints.find(e=>"OpenRouter"===e.name),t=K.endpoints.find(e=>"DeepSeek"===e.name);e?x(e):t?x(t):x(K.endpoints[0])}}else console.log("Loading fresh endpoints"),loadEndpoints()}},[c,t]),(0,s.useEffect)(()=>{if(h){let e="".concat(h.id,"_").concat(h.name),t=V.get(e);t&&Date.now()-t.timestamp<3e5?(console.log("Using cached models for endpoint:",h.name),g(t.models),_(h.id)):H===h.id&&t||(console.log("Loading fresh models for endpoint:",h.name),"OpenRouter"===h.name?loadOpenRouterModels():"DeepSeek"===h.name&&loadDeepSeekModels())}},[h,H,V]),(0,s.useEffect)(()=>{O(F.getSmartCategories())},[]);let getUsernameFromFirestore=async()=>{if(!(null==c?void 0:c.email))return"unknown";try{let{collection:e,query:t,where:r,getDocs:s}=await Promise.resolve().then(a.bind(a,4086)),n=e(l.db,"usuarios"),o=t(n,r("email","==",c.email)),i=await s(o);if(!i.empty){let e=i.docs[0];return e.data().username||e.id}return"unknown"}catch(e){return console.error("Erro ao buscar username:",e),"unknown"}},loadEndpoints=async()=>{if(!c){console.log("No user found");return}console.log("Loading endpoints for user:",c.uid,c.email),j(!0),y(null);try{let e=await getUsernameFromFirestore();console.log("Using username:",e);let t=await getUserAPIEndpoints(e);console.log("Loaded endpoints:",t),G({endpoints:t,timestamp:Date.now()}),u(t);let a=t.find(e=>"OpenRouter"===e.name),r=t.find(e=>"DeepSeek"===e.name);a?(x(a),console.log("Selected OpenRouter endpoint:",a)):r?(x(r),console.log("Selected DeepSeek endpoint:",r)):t.length>0&&(x(t[0]),console.log("Selected first endpoint:",t[0]))}catch(e){console.error("Error loading endpoints:",e),y("Erro ao carregar endpoints: "+e.message)}finally{j(!1)}},loadOpenRouterModels=async()=>{if(h&&c){j(!0),y(null);try{let e=await D.fetchModels(),t=await getUsernameFromFirestore(),a=await I.getFavoriteModelIds(t,h.id),r=e.map(e=>({...e,isFavorite:a.has(e.id)})),s="".concat(h.id,"_").concat(h.name);q(e=>new Map(e).set(s,{models:r,timestamp:Date.now()})),g(r),f(a),_(h.id)}catch(e){console.error("Error loading models:",e),y("Erro ao carregar modelos")}finally{j(!1)}}},loadDeepSeekModels=async()=>{if(h&&c){j(!0),y(null);try{let e=await A.fetchModels(),t="".concat(h.id,"_").concat(h.name);q(a=>new Map(a).set(t,{models:e,timestamp:Date.now()})),g(e),_(h.id)}catch(e){console.error("Error loading DeepSeek models:",e),y("Erro ao carregar modelos DeepSeek")}finally{j(!1)}}},es=(()=>{let e=[...p];if(e=(null==h?void 0:h.name)==="DeepSeek"?"favorites"===Z.category?[]:A.filterByCategory(e,Z.category):D.filterByCategory(e,Z.category),et&&X.trim()){let t=Y.map(e=>e.model);e=t.filter(t=>e.some(e=>e.id===t.id))}else if(z){let t=F.getModelsByCategory(e,z);e=t}if(!et||!X.trim()){let t=(null==h?void 0:h.name)==="DeepSeek"?A:D;e=t.sortModels(e,Z.sortBy)}return e})(),handleToggleFavorite=async e=>{if(c&&h){if(W.has(e.id)){console.log("Already toggling favorite for model:",e.id);return}console.log("Toggling favorite for model:",e.id,"Current status:",e.isFavorite);try{U(t=>new Set(t).add(e.id));let t=await getUsernameFromFirestore();console.log("Using username for favorites:",t);let a=await I.toggleFavorite(t,h.id,e.id,e.name);console.log("New favorite status:",a);let r=new Set(b);if(a?r.add(e.id):r.delete(e.id),f(r),g(t=>t.map(t=>t.id===e.id?{...t,isFavorite:a}:t)),h){let t="".concat(h.id,"_").concat(h.name);q(r=>{let s=r.get(t);if(s){let n=s.models.map(t=>t.id===e.id?{...t,isFavorite:a}:t);return new Map(r).set(t,{models:n,timestamp:s.timestamp})}return r})}}catch(e){console.error("Error toggling favorite:",e)}finally{U(t=>{let a=new Set(t);return a.delete(e.id),a})}}},isExpensiveModel=e=>{if((null==h?void 0:h.name)!=="OpenRouter")return!1;let t=D.getTotalPrice(e);return t>2e-5},handleSelectModel=e=>{er(e.id),isExpensiveModel(e)?(P(e),E(!0)):(d(e.id),o())},handleUseCustomModel=()=>{C.trim()&&(d(C.trim()),o())};return t?(0,r.jsxs)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl"}),(0,r.jsxs)("div",{className:"p-6 border-b border-blue-700/30 relative z-10",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-blue-100",children:"Selecionar Modelo"})]}),(0,r.jsx)("button",{onClick:o,className:"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-blue-200",children:"Endpoint"}),(0,r.jsx)("button",{onClick:()=>{if(h){let e="".concat(h.id,"_").concat(h.name);q(t=>{let a=new Map(t);return a.delete(e),a}),console.log("Forcing refresh for endpoint:",h.name),"OpenRouter"===h.name?loadOpenRouterModels():"DeepSeek"===h.name&&loadDeepSeekModels()}},disabled:v||!h,className:"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",title:"Atualizar modelos",children:(0,r.jsx)("svg",{className:"w-4 h-4 ".concat(v?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),(0,r.jsxs)("select",{value:(null==h?void 0:h.id)||"",onChange:e=>{let t=m.find(t=>t.id===e.target.value);x(t||null)},className:"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200",children:[(0,r.jsx)("option",{value:"",children:"Selecione um endpoint"}),m.map(e=>(0,r.jsx)("option",{value:e.id,className:"bg-blue-900 text-blue-100",children:e.name},e.id))]})]})]}),(null==h?void 0:h.name)==="OpenRouter"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"p-6 border-b border-blue-700/30 space-y-6 relative z-10",children:[(0,r.jsxs)("div",{className:"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})}),(0,r.jsx)("span",{children:"Modelo Customizado"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("input",{type:"text",placeholder:"openai/gpt-4-1",value:C,onChange:e=>M(e.target.value),onKeyDown:e=>"Enter"===e.key&&handleUseCustomModel(),className:"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"}),(0,r.jsx)("button",{onClick:handleUseCustomModel,disabled:!C.trim(),className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100",children:"Usar"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-300/70 mt-3",children:"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)"})]}),(0,r.jsx)("div",{className:"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20",children:["paid","free","favorites"].map(e=>(0,r.jsx)("button",{onClick:()=>$(t=>({...t,category:e})),className:"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 ".concat(Z.category===e?"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg":"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30"),children:"paid"===e?"Pagos":"free"===e?"Gr\xe1tis":"Favoritos"},e))}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(components_AdvancedSearchInput,{value:X,onChange:J,suggestions:[],isSearching:ee,placeholder:"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')",showSuggestions:!1})}),(0,r.jsxs)("select",{value:Z.sortBy,onChange:e=>$(t=>({...t,sortBy:e.target.value})),className:"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200",disabled:et&&X.trim().length>0,children:[(0,r.jsx)("option",{value:"newest",className:"bg-blue-900 text-blue-100",children:"Mais recentes"}),(0,r.jsx)("option",{value:"price_low",className:"bg-blue-900 text-blue-100",children:"Menor pre\xe7o"}),(0,r.jsx)("option",{value:"price_high",className:"bg-blue-900 text-blue-100",children:"Maior pre\xe7o"}),(0,r.jsx)("option",{value:"context_high",className:"bg-blue-900 text-blue-100",children:"Maior contexto"})]})]}),!et&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:()=>B(null),className:"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ".concat(z?"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200":"bg-blue-600 text-white"),children:"Todos"}),R.slice(0,6).map(e=>(0,r.jsxs)("button",{onClick:()=>B(e.id),className:"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 ".concat(z===e.id?"bg-blue-600 text-white":"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200"),title:e.description,children:[(0,r.jsx)("span",{children:e.icon}),(0,r.jsx)("span",{children:e.name})]},e.id))]}),et&&X.trim()&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-blue-300",children:[es.length," resultado",1!==es.length?"s":"",' para "',X,'"']}),(0,r.jsx)("button",{onClick:ea,className:"text-blue-400 hover:text-blue-300 transition-colors duration-200",children:"Limpar busca"})]})]})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10",children:[v&&(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"}),(0,r.jsx)("span",{className:"text-blue-200 text-sm font-medium",children:"Carregando modelos..."})]})}),w&&(0,r.jsx)("div",{className:"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("span",{className:"text-red-300 font-medium",children:w})]})}),!v&&!w&&0===es.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,r.jsx)("p",{className:"text-blue-300 font-medium",children:et&&X.trim()?'Nenhum resultado para "'.concat(X,'"'):z?"Nenhum modelo na categoria selecionada":"Nenhum modelo encontrado"}),(0,r.jsx)("div",{className:"text-blue-400/70 text-sm mt-2 space-y-1",children:et&&X.trim()?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"Tente:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-left max-w-xs mx-auto",children:[(0,r.jsx)("li",{children:"Verificar a ortografia"}),(0,r.jsx)("li",{children:"Usar termos mais gen\xe9ricos"}),(0,r.jsx)("li",{children:"Explorar as categorias"}),(0,r.jsx)("li",{children:"Limpar filtros ativos"})]})]}):(0,r.jsx)("p",{children:"Tente ajustar os filtros ou usar as categorias"})})]}),(0,r.jsx)("div",{className:"space-y-3",children:es.slice(0,N).map(e=>{let t=et?Y.find(t=>t.model.id===e.id):null;return(0,r.jsx)(ModelCard,{model:e,isSelected:i===e.id,onSelect:()=>handleSelectModel(e),onToggleFavorite:()=>handleToggleFavorite(e),isToggling:W.has(e.id),service:D,searchTerm:et?X:"",searchResult:t},e.id)})}),!v&&!w&&es.length>N&&(0,r.jsx)("div",{className:"flex justify-center mt-6",children:(0,r.jsxs)("button",{onClick:()=>{k(e=>e+4)},className:"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105",children:[(0,r.jsx)("span",{children:"Carregar mais modelos"}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),!v&&!w&&es.length>0&&(0,r.jsxs)("div",{className:"text-center mt-4 space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm text-blue-400/70",children:["Mostrando ",Math.min(N,es.length)," de ",es.length," modelos",p.length!==es.length&&(0,r.jsxs)("span",{className:"ml-2 text-blue-300",children:["(",p.length," total)"]})]}),et&&X.trim()&&(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-xs text-blue-400/60",children:[(0,r.jsxs)("span",{children:["\uD83D\uDD0D Busca: ",X]}),Y.length>0&&(0,r.jsxs)("span",{children:["⚡ ",Y.length," resultados"]})]})]})]})]}),(null==h?void 0:h.name)==="DeepSeek"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"p-6 border-b border-slate-700/30 space-y-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-slate-100 mb-2",children:"Modelos DeepSeek"}),(0,r.jsx)("p",{className:"text-sm text-slate-400",children:"Escolha entre nossos modelos especializados"})]})}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:[v&&(0,r.jsx)("div",{className:"flex justify-center py-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"}),(0,r.jsx)("span",{className:"text-blue-200 text-sm font-medium",children:"Carregando modelos..."})]})}),w&&(0,r.jsx)("div",{className:"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("span",{className:"text-red-300 font-medium",children:w})]})}),!v&&!w&&0===p.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,r.jsx)("p",{className:"text-blue-300 font-medium",children:"Nenhum modelo encontrado"}),(0,r.jsx)("p",{className:"text-blue-400/70 text-sm mt-1",children:"Tente ajustar os filtros de busca"})]}),!v&&!w&&p.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:p.map(e=>(0,r.jsx)(DeepSeekModelCard,{model:e,isSelected:i===e.id,onSelect:()=>handleSelectModel(e)},e.id))})]})]}),(null==h?void 0:h.name)!=="OpenRouter"&&(null==h?void 0:h.name)!=="DeepSeek"&&h&&(0,r.jsxs)("div",{className:"p-8 text-center relative z-10",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("p",{className:"text-blue-300 font-medium",children:"Sele\xe7\xe3o de modelos dispon\xedvel para OpenRouter e DeepSeek"}),(0,r.jsx)("p",{className:"text-blue-400/70 text-sm mt-1",children:"Selecione um desses endpoints para ver os modelos dispon\xedveis"})]})]}),(0,r.jsx)(dashboard_ExpensiveModelConfirmationModal,{isOpen:S,model:L,onConfirm:()=>{L&&(er(L.id),d(L.id),E(!1),P(null),o())},onCancel:()=>{E(!1),P(null)}})]}):null},P=a(2549),z=a(2442);function AttachmentsModal(e){let{isOpen:t,onClose:a,attachments:n,activeAttachments:o,onToggleAttachment:l}=e,[i,d]=(0,s.useState)(null);(0,s.useEffect)(()=>{let handleEsc=e=>{"Escape"===e.key&&a()};return t&&(document.addEventListener("keydown",handleEsc),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",handleEsc),document.body.style.overflow="unset"}},[t,a]);let formatFileSize=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},formatDate=e=>new Date(e).toLocaleString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),handleImageClick=e=>{d(e)},handleDownload=e=>{let t=document.createElement("a");t.href=e.url,t.download=e.filename,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t)},isAttachmentActive=e=>o.includes(e),u=n.filter(e=>isAttachmentActive(e.id)).length,h=n.length;return t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.M,{children:t&&(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:a,children:(0,r.jsxs)(c.E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-blue-600/30 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl",onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border-b border-blue-600/30 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Gerenciar Anexos"}),(0,r.jsxs)("p",{className:"text-blue-200 text-sm",children:[u," de ",h," anexos ativos no contexto"]})]})]}),(0,r.jsx)("button",{onClick:a,className:"p-2 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 border border-blue-600/20",children:(0,r.jsx)(P.Z,{className:"w-5 h-5"})})]})}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:0===n.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-500/30",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum anexo encontrado"}),(0,r.jsx)("p",{className:"text-blue-200",children:"Este chat ainda n\xe3o possui anexos enviados."})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-medium",children:"Controles R\xe1pidos"}),(0,r.jsx)("p",{className:"text-blue-200 text-sm",children:"Ativar ou desativar todos os anexos"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>n.forEach(e=>{isAttachmentActive(e.id)||l(e.id)}),className:"px-4 py-2 bg-green-600/20 hover:bg-green-600/30 text-green-300 hover:text-green-200 rounded-lg border border-green-600/30 transition-all duration-200 text-sm",children:"Ativar Todos"}),(0,r.jsx)("button",{onClick:()=>n.forEach(e=>{isAttachmentActive(e.id)&&l(e.id)}),className:"px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-red-300 hover:text-red-200 rounded-lg border border-red-600/30 transition-all duration-200 text-sm",children:"Desativar Todos"})]})]})}),(0,r.jsx)("div",{className:"grid gap-4",children:n.map(e=>{let t=isAttachmentActive(e.id);return(0,r.jsx)(c.E.div,{layout:!0,className:"\n                              bg-blue-800/30 backdrop-blur-sm border rounded-xl p-4 transition-all duration-200\n                              ".concat(t?"border-green-500/50 shadow-lg shadow-green-500/10":"border-blue-600/30 opacity-60","\n                            "),children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0 pt-1",children:(0,r.jsx)("button",{onClick:()=>l(e.id),className:"\n                                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200\n                                    ".concat(t?"bg-green-600":"bg-gray-600","\n                                  "),children:(0,r.jsx)("span",{className:"\n                                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200\n                                      ".concat(t?"translate-x-6":"translate-x-1","\n                                    ")})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:["image"===e.type?(0,r.jsx)(w.Z,{className:"w-5 h-5 text-blue-400 flex-shrink-0"}):(0,r.jsx)(k.Z,{className:"w-5 h-5 text-red-400 flex-shrink-0"}),(0,r.jsx)("h4",{className:"text-white font-medium truncate",children:e.filename}),t&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 bg-green-600/20 px-2 py-1 rounded-full border border-green-600/30",children:[(0,r.jsx)(z.Z,{className:"w-3 h-3 text-green-400"}),(0,r.jsx)("span",{className:"text-green-300 text-xs font-medium",children:"Ativo"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-blue-200 mb-3",children:[(0,r.jsx)("span",{children:formatFileSize(e.size)}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:formatDate(e.uploadedAt)}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:"capitalize",children:e.type})]}),"image"===e.type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("img",{src:e.url,alt:e.filename,className:"max-w-32 h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity border border-blue-600/30",onClick:()=>handleImageClick(e.url)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["image"===e.type&&(0,r.jsxs)("button",{onClick:()=>handleImageClick(e.url),className:"flex items-center space-x-1 px-3 py-1.5 bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 rounded-lg border border-blue-600/30 transition-all duration-200 text-sm",children:[(0,r.jsx)(y.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Visualizar"})]}),(0,r.jsxs)("button",{onClick:()=>handleDownload(e),className:"flex items-center space-x-1 px-3 py-1.5 bg-gray-600/20 hover:bg-gray-600/30 text-gray-300 hover:text-gray-200 rounded-lg border border-gray-600/30 transition-all duration-200 text-sm",children:[(0,r.jsx)(N.Z,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Download"})]})]})]})]})},e.id)})})]})}),(0,r.jsx)("div",{className:"bg-blue-800/30 backdrop-blur-sm border-t border-blue-600/30 p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-blue-200",children:(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Dica:"})," Anexos desativados n\xe3o ser\xe3o inclu\xeddos no contexto da conversa, mas permanecer\xe3o salvos no chat."]})}),(0,r.jsx)("button",{onClick:a,className:"px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-all duration-200 font-medium",children:"Fechar"})]})})]})})}),(0,r.jsx)(m.M,{children:i&&(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-[60] flex items-center justify-center p-4",onClick:()=>d(null),children:(0,r.jsxs)(c.E.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},className:"relative max-w-[90vw] max-h-[90vh]",onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("img",{src:i,alt:"Imagem expandida",className:"max-w-full max-h-full object-contain rounded-lg"}),(0,r.jsx)("button",{onClick:()=>d(null),className:"absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-all duration-200",children:(0,r.jsx)(P.Z,{className:"w-5 h-5"})})]})})})]}):null}var B=new class{calculateMessageStatistics(e,t){let a=this.filterMessages(e,t);if(0===a.length)return this.getEmptyStatistics();let r=a.filter(e=>"user"===e.role),s=a.filter(e=>"assistant"===e.role),n=this.countWords(r.map(e=>e.content).join(" ")),o=this.countWords(s.map(e=>e.content).join(" ")),l=n+o,i=r.reduce((e,t)=>e+t.content.length,0),d=s.reduce((e,t)=>e+t.content.length,0),c=i+d,m=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.prompt_tokens)||0)},0),u=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.completion_tokens)||0)},0),h=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.cost)||0)},0),x=s.filter(e=>e.responseTime).map(e=>e.responseTime),p=x.length>0?x.reduce((e,t)=>e+t,0)/x.length:0,g=c/a.length,b=l/a.length,f=a.reduce((e,t)=>e+(t.content.match(/[.!?]+/g)||[]).length,0),v=f/a.length,j=this.getMostUsedWords(a.map(e=>e.content).join(" "));return{totalMessages:a.length,totalWords:l,totalWordsAI:o,totalWordsUser:n,totalPromptTokens:m,totalCompletionTokens:u,totalCost:h,averageResponseTime:p,averageMessageLength:g,averageWordsPerMessage:b,averageSentencesPerMessage:v,estimatedReadingTime:Math.ceil(l/250),totalCharactersAI:d,totalCharactersUser:i,totalCharacters:c,mostUsedWords:j}}calculateDetailedStatistics(e,t,a){let r=this.calculateMessageStatistics(e,a),s=this.filterMessages(e,a);return{...r,dailyStats:this.calculateDailyStats(s),weeklyStats:this.calculateWeeklyStats(s),monthlyStats:this.calculateMonthlyStats(s),modelStats:this.calculateModelStats(s),attachmentStats:this.calculateAttachmentStats(s),timeStats:this.calculateTimeStats(s,t),favoriteStats:this.calculateFavoriteStats(s)}}filterMessages(e,t){if(!t)return e;let a=[...e];return t.dateRange&&(a=a.filter(e=>{let a=new Date(e.timestamp);return a>=t.dateRange.start&&a<=t.dateRange.end})),a}countWords(e){return e.trim().split(/\s+/).filter(e=>e.length>0).length}getMostUsedWords(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=e.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(e=>e.length>2),r=new Map;a.forEach(e=>{r.set(e,(r.get(e)||0)+1)});let s=a.length,n=Array.from(r.entries()).sort((e,t)=>t[1]-e[1]).slice(0,t).map(e=>{let[t,a]=e;return{word:t,count:a,percentage:a/s*100}});return n}calculateDailyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp).toISOString().split("T")[0];t.has(a)||t.set(a,{messages:[],date:a}),t.get(a).messages.push(e)}),Array.from(t.values()).map(e=>{let t=this.countWords(e.messages.map(e=>e.content).join(" ")),a=e.messages.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.total_tokens)||0)},0),r=e.messages.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.cost)||0)},0),s=e.messages.filter(e=>e.responseTime).map(e=>e.responseTime),n=s.length>0?s.reduce((e,t)=>e+t,0)/s.length:0;return{date:e.date,messages:e.messages.length,words:t,tokens:a,cost:r,averageResponseTime:n}}).sort((e,t)=>e.date.localeCompare(t.date))}calculateWeeklyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp),r=a.getFullYear(),s=this.getWeekNumber(a),n="".concat(r,"-W").concat(s.toString().padStart(2,"0"));t.has(n)||t.set(n,[]),t.get(n).push(e)}),Array.from(t.entries()).map(e=>{let[t,a]=e,r=this.countWords(a.map(e=>e.content).join(" ")),s=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.total_tokens)||0)},0),n=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.cost)||0)},0),o=a.filter(e=>e.responseTime).map(e=>e.responseTime),l=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0;return{week:t,messages:a.length,words:r,tokens:s,cost:n,averageResponseTime:l}}).sort((e,t)=>e.week.localeCompare(t.week))}calculateMonthlyStats(e){let t=new Map;return e.forEach(e=>{let a=new Date(e.timestamp),r="".concat(a.getFullYear(),"-").concat((a.getMonth()+1).toString().padStart(2,"0"));t.has(r)||t.set(r,[]),t.get(r).push(e)}),Array.from(t.entries()).map(e=>{let[t,a]=e,r=this.countWords(a.map(e=>e.content).join(" ")),s=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.total_tokens)||0)},0),n=a.reduce((e,t)=>{var a;return e+((null===(a=t.usage)||void 0===a?void 0:a.cost)||0)},0),o=a.filter(e=>e.responseTime).map(e=>e.responseTime),l=o.length>0?o.reduce((e,t)=>e+t,0)/o.length:0;return{month:t,messages:a.length,words:r,tokens:s,cost:n,averageResponseTime:l}}).sort((e,t)=>e.month.localeCompare(t.month))}getWeekNumber(e){let t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate())),a=t.getUTCDay()||7;t.setUTCDate(t.getUTCDate()+4-a);let r=new Date(Date.UTC(t.getUTCFullYear(),0,1));return Math.ceil(((t.getTime()-r.getTime())/864e5+1)/7)}calculateModelStats(e){return[]}calculateAttachmentStats(e){let t=e.filter(e=>e.attachments&&e.attachments.length>0),a=t.flatMap(e=>e.attachments||[]),r=a.filter(e=>"image"===e.type).length,s=a.filter(e=>"pdf"===e.type).length,n=a.reduce((e,t)=>e+t.size,0),o=a.length>0?n/a.length:0,l={};return a.forEach(e=>{l[e.type]=(l[e.type]||0)+1}),{totalAttachments:a.length,imageAttachments:r,pdfAttachments:s,totalSize:n,averageSize:o,attachmentsByType:l}}calculateTimeStats(e,t){let a={},r={},s=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(let e=0;e<24;e++)a[e]=0;s.forEach(e=>{r[e]=0}),e.forEach(e=>{let t=new Date(e.timestamp),n=t.getHours(),o=s[t.getDay()];a[n]++,r[o]++});let n=Object.entries(a).reduce((e,t)=>{let[a,r]=t;return r>e.count?{hour:parseInt(a),count:r}:e},{hour:0,count:0}).hour,o=Object.entries(r).reduce((e,t)=>{let[a,r]=t;return r>e.count?{day:a,count:r}:e},{day:"monday",count:0}).day,l=0,i=0,d=0,c=0,m=0;if(t){let e=t.filter(e=>e.sessionTime).map(e=>e.sessionTime.totalTime);e.length>0&&(i=(l=e.reduce((e,t)=>e+t,0))/e.length,d=Math.max(...e),c=Math.min(...e),m=e.length)}return{totalSessionTime:l,averageSessionTime:i,longestSession:d,shortestSession:c,sessionsCount:m,mostActiveHour:n,mostActiveDay:o,timeDistribution:a}}calculateFavoriteStats(e){let t=e.filter(e=>e.isFavorite),a={user:t.filter(e=>"user"===e.role).length,assistant:t.filter(e=>"assistant"===e.role).length},r=t.length>0?t.reduce((e,t)=>e+t.content.length,0)/t.length:0,s=t.length>0?this.getMostUsedWords(t.map(e=>e.content).join(" "),5):[];return{totalFavorites:t.length,favoritesByRole:a,averageFavoriteLength:r,mostFavoritedWords:s}}getEmptyStatistics(){return{totalMessages:0,totalWords:0,totalWordsAI:0,totalWordsUser:0,totalPromptTokens:0,totalCompletionTokens:0,totalCost:0,averageResponseTime:0,averageMessageLength:0,averageWordsPerMessage:0,averageSentencesPerMessage:0,estimatedReadingTime:0,totalCharactersAI:0,totalCharactersUser:0,totalCharacters:0,mostUsedWords:[]}}};function StatisticsModal(e){var t;let{isOpen:a,onClose:n,messages:o,chatName:l="Todas as Conversas"}=e,[i,d]=(0,s.useState)(null),[c,m]=(0,s.useState)(!0),[u,h]=(0,s.useState)("overview");if((0,s.useEffect)(()=>{if(a&&o.length>0){m(!0);try{let e=B.calculateMessageStatistics(o);d(e)}catch(e){console.error("Erro ao calcular estat\xedsticas:",e)}finally{m(!1)}}},[a,o]),!a)return null;let formatNumber=e=>new Intl.NumberFormat("pt-BR").format(Math.round(e)),formatCurrency=e=>new Intl.NumberFormat("pt-BR",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),getIconSvg=e=>{let t={message:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",text:"M4 6h16M4 12h16M4 18h7",robot:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",input:"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",output:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14",dollar:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",clock:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",length:"M7 16l-4-4m0 0l4-4m-4 4h18",average:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",sentence:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",read:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253",total:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"};return t[e]||t.message},getColorClasses=e=>({blue:"from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-300",green:"from-green-500/20 to-green-600/20 border-green-500/30 text-green-300",purple:"from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-300",orange:"from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-300",red:"from-red-500/20 to-red-600/20 border-red-500/30 text-red-300",cyan:"from-cyan-500/20 to-cyan-600/20 border-cyan-500/30 text-cyan-300"})[e];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border border-blue-700/30 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-blue-700/30",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-white flex items-center gap-3",children:[(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Estat\xedsticas"]}),(0,r.jsx)("p",{className:"text-blue-300 mt-1",children:l})]}),(0,r.jsx)("button",{onClick:n,className:"text-blue-300 hover:text-white transition-colors p-2 hover:bg-blue-800/30 rounded-lg",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"flex border-b border-blue-700/30",children:[{id:"overview",label:"Vis\xe3o Geral",icon:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},{id:"details",label:"Detalhes",icon:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},{id:"charts",label:"Gr\xe1ficos",icon:"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"}].map(e=>(0,r.jsxs)("button",{onClick:()=>h(e.id),className:"flex items-center gap-2 px-6 py-3 font-medium transition-all ".concat(u===e.id?"text-blue-300 border-b-2 border-blue-400 bg-blue-900/20":"text-blue-400 hover:text-blue-300 hover:bg-blue-900/10"),children:[(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:e.icon})}),e.label]},e.id))}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-200px)]",children:c?(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"}),(0,r.jsx)("span",{className:"ml-3 text-blue-300",children:"Calculando estat\xedsticas..."})]}):i?(0,r.jsxs)(r.Fragment,{children:["overview"===u&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:(i?[{title:"Total de Mensagens",value:formatNumber(i.totalMessages),icon:"message",color:"blue"},{title:"Total de Palavras",value:formatNumber(i.totalWords),icon:"text",color:"green"},{title:"Palavras da IA",value:formatNumber(i.totalWordsAI),icon:"robot",color:"purple"},{title:"Palavras do Usu\xe1rio",value:formatNumber(i.totalWordsUser),icon:"user",color:"orange"},{title:"Tempo M\xe9dio de Resposta",value:(t=i.averageResponseTime)<1e3?"".concat(Math.round(t),"ms"):t<6e4?"".concat((t/1e3).toFixed(1),"s"):"".concat((t/6e4).toFixed(1),"min"),icon:"clock",color:"purple"},{title:"Comprimento M\xe9dio",value:formatNumber(i.averageMessageLength),subtitle:"caracteres",icon:"length",color:"orange"},{title:"Palavras por Mensagem",value:formatNumber(i.averageWordsPerMessage),icon:"average",color:"cyan"},{title:"Frases por Mensagem",value:formatNumber(i.averageSentencesPerMessage),icon:"sentence",color:"blue"},{title:"Tempo de Leitura",value:"".concat(i.estimatedReadingTime,"min"),icon:"read",color:"green"},{title:"Caracteres da IA",value:formatNumber(i.totalCharactersAI),icon:"robot",color:"purple"},{title:"Caracteres do Usu\xe1rio",value:formatNumber(i.totalCharactersUser),icon:"user",color:"orange"},{title:"Total de Caracteres",value:formatNumber(i.totalCharacters),icon:"total",color:"cyan"}]:[]).map((e,t)=>(0,r.jsxs)("div",{className:"bg-gradient-to-br ".concat(getColorClasses(e.color)," border rounded-xl p-4 hover:scale-105 transition-transform duration-200"),children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:getIconSvg(e.icon)})})}),(0,r.jsx)("h3",{className:"text-sm font-medium text-white/80 mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:e.value}),e.subtitle&&(0,r.jsx)("p",{className:"text-xs text-white/60 mt-1",children:e.subtitle})]},t))}),"details"===u&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),"Palavras Mais Usadas"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:i.mostUsedWords.slice(0,9).map((e,t)=>(0,r.jsxs)("div",{className:"bg-blue-800/30 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-white font-medium",children:["#",t+1]}),(0,r.jsxs)("span",{className:"text-blue-300 text-sm",children:[e.percentage.toFixed(1),"%"]})]}),(0,r.jsx)("p",{className:"text-lg font-semibold text-white",children:e.word}),(0,r.jsxs)("p",{className:"text-blue-400 text-sm",children:[formatNumber(e.count)," vezes"]})]},t))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Distribui\xe7\xe3o por Usu\xe1rio"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-blue-300",children:"Mensagens do Usu\xe1rio"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:formatNumber(o.filter(e=>"user"===e.role).length)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-blue-300",children:"Mensagens da IA"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:formatNumber(o.filter(e=>"assistant"===e.role).length)})]}),(0,r.jsx)("div",{className:"w-full bg-blue-800/30 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat(o.filter(e=>"user"===e.role).length/o.length*100,"%")}})})]})]}),(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-700/30 rounded-xl p-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),"An\xe1lise de Custos"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-blue-300",children:"Custo por Mensagem"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:formatCurrency(i.totalCost/i.totalMessages)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-blue-300",children:"Custo por Token"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:formatCurrency(i.totalCost/(i.totalPromptTokens+i.totalCompletionTokens))})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-blue-300",children:"Custo por Palavra"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:formatCurrency(i.totalCost/i.totalWords)})]})]})]})]})]}),"charts"===u&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("svg",{className:"w-16 h-16 text-blue-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Gr\xe1ficos em Desenvolvimento"}),(0,r.jsx)("p",{className:"text-blue-300",children:"Os gr\xe1ficos interativos estar\xe3o dispon\xedveis em breve para uma melhor visualiza\xe7\xe3o dos dados."})]})})]}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-blue-300",children:"Nenhuma estat\xedstica dispon\xedvel"})})})]})})}let R=new class{async sendMessage(e,t,a,r){try{this.abortController=new AbortController;let r={username:e.username,chatId:e.chatId,message:e.message,model:e.model,attachments:e.attachments||[],isRegeneration:e.isRegeneration||!1,webSearchEnabled:e.webSearchEnabled||!1};console.log("=== DEBUG: AI SERVICE REQUEST DATA ==="),console.log("Request data:",JSON.stringify(r,null,2)),console.log("Attachments length:",r.attachments.length),r.attachments.length>0&&console.log("First attachment:",JSON.stringify(r.attachments[0],null,2));let s=await fetch(this.functionUrl,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r),signal:this.abortController.signal});if(console.log("\uD83C\uDF10 URL:",s.url),console.groupEnd(),!s.ok){let e=await s.json();throw console.group("❌ AI SERVICE - ERRO NA RESPOSTA"),console.error("Status:",s.status),console.error("Status Text:",s.statusText),console.error("Error Data:",e),console.groupEnd(),Error(e.error||"HTTP ".concat(s.status,": ").concat(s.statusText))}if(!s.body)throw Error("Response body is not available");let n=s.body.getReader(),o=new TextDecoder,l="";try{for(;;){let{done:e,value:a}=await n.read();if(e)break;let r=o.decode(a,{stream:!0});l+=r,t(r)}a(l)}finally{n.releaseLock()}}catch(e){if(e instanceof Error){if("AbortError"===e.name)return;r(e.message)}else r("Erro desconhecido na comunica\xe7\xe3o com a IA")}finally{this.abortController=null}}cancelRequest(){this.abortController&&(this.abortController.abort(),this.abortController=null)}isRequestInProgress(){return null!==this.abortController}async loadChatMessages(e,t){try{let a=await fetch("/api/chat/".concat(e,"/").concat(t),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)throw Error("Erro ao carregar chat: ".concat(a.statusText));let r=await a.json();return r.messages||[]}catch(e){return console.error("Erro ao carregar mensagens do chat:",e),[]}}convertToAIFormat(e){return e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:e.timestamp,isFavorite:e.isFavorite||!1,attachments:e.attachments||[]}))}convertFromAIFormat(e){return e.map(e=>({id:e.id,content:e.content,sender:"user"===e.role?"user":"ai",timestamp:e.timestamp,isFavorite:e.isFavorite||!1,attachments:e.attachments||[]}))}generateMessageId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}validateConfig(e){var t,a,r;if(!(null===(t=e.username)||void 0===t?void 0:t.trim()))throw Error("Username \xe9 obrigat\xf3rio");if(!(null===(a=e.chatId)||void 0===a?void 0:a.trim()))throw Error("Chat ID \xe9 obrigat\xf3rio");if(!(null===(r=e.message)||void 0===r?void 0:r.trim()))throw Error("Mensagem \xe9 obrigat\xf3ria");if(e.message.length>1e4)throw Error("Mensagem muito longa (m\xe1ximo 10.000 caracteres)")}async sendMessageSafe(e,t,a,r){try{this.validateConfig(e),await this.sendMessage(e,t,a,r)}catch(e){e instanceof Error?r(e.message):r("Erro de valida\xe7\xe3o")}}async deleteMessage(e,t,a){try{let r=await fetch("/api/chat/".concat(e,"/").concat(t,"/message/").concat(a),{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error("Erro ao deletar mensagem: ".concat(r.statusText));return!0}catch(e){return console.error("Erro ao deletar mensagem:",e),!1}}async updateMessage(e,t,a,r){try{let s=await fetch("/api/chat/".concat(e,"/").concat(t,"/message/").concat(a),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r})});if(!s.ok)throw Error("Erro ao atualizar mensagem: ".concat(s.statusText));return!0}catch(e){return console.error("Erro ao atualizar mensagem:",e),!1}}constructor(){this.functionUrl="https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI",this.abortController=null}};function ChatArea(e){let{currentChat:t,onChatCreated:a,onUpdateOpenRouterBalance:o}=e,{user:c}=(0,n.useAuth)(),[m,u]=(0,s.useState)(""),[h,x]=(0,s.useState)(!1),[p,g]=(0,s.useState)([]),[b,f]=(0,s.useState)("meta-llama/llama-3.1-8b-instruct:free"),[v,j]=(0,s.useState)(t),[w,y]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[C,M]=(0,s.useState)(!1),[S,E]=(0,s.useState)(!1),[L,D]=(0,s.useState)(!1),[A,I]=(0,s.useState)(null),[T,F]=(0,s.useState)("Nova Conversa"),[P,z]=(0,s.useState)(!1),[B,O]=(0,s.useState)(void 0),W=(0,s.useRef)(null);(0,s.useEffect)(()=>{let loadUsername=async()=>{if(null==c?void 0:c.email){let e=await getUsernameFromFirestore();O(e)}};loadUsername()},[null==c?void 0:c.email]);let getUsernameFromFirestore=async()=>{if(!(null==c?void 0:c.email))return"unknown";try{let e=(0,i.collection)(l.db,"usuarios"),t=(0,i.query)(e,(0,i.where)("email","==",c.email)),a=await (0,i.getDocs)(t);if(!a.empty){let e=a.docs[0],t=e.data();return t.username||c.email.split("@")[0]}return c.email.split("@")[0]}catch(e){return console.error("Erro ao buscar username:",e),c.email.split("@")[0]}},saveLastUsedModelForChat=async(e,t)=>{if(c&&t)try{let a=await getUsernameFromFirestore(),r=(0,i.doc)(l.db,"usuarios",a,"conversas",t);await (0,i.r7)(r,{lastUsedModel:e,lastModelUpdateAt:Date.now()}),console.log("Last used model saved for chat:",{chatId:t,modelId:e})}catch(e){console.error("Error saving last used model for chat:",e)}},loadLastUsedModelForChat=async e=>{if(c&&e)try{let t=await getUsernameFromFirestore(),a=(0,i.doc)(l.db,"usuarios",t,"conversas",e),r=await (0,i.getDoc)(a);if(r.exists()){let t=r.data();t.lastUsedModel?(f(t.lastUsedModel),console.log("Loaded last used model for chat:",{chatId:e,model:t.lastUsedModel})):(f("meta-llama/llama-3.1-8b-instruct:free"),console.log("No saved model for chat, using default:",e))}}catch(e){console.error("Error loading last used model for chat:",e)}},loadGlobalLastUsedModel=async()=>{if(c)try{let e=await getUsernameFromFirestore(),t=(0,i.doc)(l.db,"usuarios",e,"configuracoes","settings"),a=await (0,i.getDoc)(t);if(a.exists()){let e=a.data();e.lastUsedModel&&(f(e.lastUsedModel),console.log("Loaded global last used model:",e.lastUsedModel))}}catch(e){console.error("Error loading global last used model:",e)}},handleModelChange=e=>{f(e),v&&saveLastUsedModelForChat(e,v)},createAutoChat=async e=>{if(!(null==c?void 0:c.email))return null;try{let t=(0,i.collection)(l.db,"usuarios"),a=(0,i.query)(t,(0,i.where)("email","==",c.email)),r=await (0,i.getDocs)(a);if(r.empty)return null;let s=r.docs[0],n=s.data(),o=n.username,m=Date.now(),u=Math.random().toString(36).substring(2,8),h="chat_".concat(m,"_").concat(u),x=new Date().toISOString(),p="Nova Conversa";if(e.trim().length>0){let t=e.trim().split(" "),a=t.slice(0,Math.min(4,t.length)).join(" ");p=a.length>30?a.substring(0,30)+"...":a}let g={context:"",createdAt:x,folderId:null,frequencyPenalty:1,isFixed:!1,lastUpdatedAt:x,lastUsedModel:b,latexInstructions:!1,maxTokens:2048,name:p,password:"",repetitionPenalty:1,sessionTime:{lastSessionStart:x,lastUpdated:x,totalTime:0},systemPrompt:"",temperature:1,ultimaMensagem:e||"Anexo enviado",ultimaMensagemEm:x,updatedAt:x};await (0,i.pl)((0,i.doc)(l.db,"usuarios",o,"conversas",h),g);let f={id:h,name:p,messages:[],createdAt:x,lastUpdated:x},v=new Blob([JSON.stringify(f,null,2)],{type:"application/json"}),j=(0,d.iH)(l.tO,"usuarios/".concat(o,"/conversas/").concat(h,"/chat.json"));return await (0,d.KV)(j,v),console.log("Chat criado automaticamente:",h),F(p),h}catch(e){return console.error("Erro ao criar chat automaticamente:",e),null}},handleSendMessage=async(e,t)=>{let r=getAllChatAttachments().filter(e=>!1!==e.isActive),s=[...e||[],...r],n=s.filter((e,t,a)=>t===a.findIndex(t=>t.id===e.id));console.log("\uD83D\uDE80 RESULTADO FINAL - ANEXOS QUE SER\xc3O ENVIADOS PARA A IA:",n.length),n.forEach(e=>{console.log("\uD83D\uDE80 Enviando para IA: ".concat(e.filename," (ID: ").concat(e.id,", isActive: ").concat(e.isActive,")"))}),0===n.length&&console.log("❌ NENHUM ANEXO SER\xc1 ENVIADO PARA A IA"),console.log("=== DEBUG: MENSAGENS ATUAIS NO ESTADO ==="),console.log("Total de mensagens no estado:",p.length);let l=p.filter(e=>e.attachments&&e.attachments.length>0);if(console.log("Mensagens com anexos no estado:",l.length),l.forEach((e,t)=>{console.log("Mensagem ".concat(t+1," com anexos:"),{id:e.id,sender:e.sender,attachmentsCount:e.attachments&&e.attachments.length||0,attachments:e.attachments})}),!m.trim()&&(!e||0===e.length)||h||L){console.log("=== DEBUG: CONDI\xc7\xc3O DE RETORNO ==="),console.log("Mensagem vazia:",!m.trim()),console.log("Sem anexos:",!e||0===e.length),console.log("Loading:",h),console.log("Streaming:",L);return}if(!(null==c?void 0:c.email))return;let i={id:R.generateMessageId(),content:m.trim(),sender:"user",timestamp:new Date().toISOString(),attachments:e||[]},d=v;if(!d){let t=m.trim()||(e&&e.length>0?"Anexo enviado":"Nova conversa");(d=await createAutoChat(t))&&(j(d),loadChatName(d),null==a||a(d))}if(!d){console.error("N\xe3o foi poss\xedvel criar ou obter chat ID");return}g(e=>[...e,i]);let f=m.trim()||"";u(""),x(!0),D(!0);let w=R.generateMessageId();I(w);let y=await getUsernameFromFirestore();await R.sendMessageSafe({username:y,chatId:d,message:f,model:b,attachments:n,webSearchEnabled:t},e=>{g(a=>{let r=a.findIndex(e=>e.id===w);if(-1!==r)return a.map(t=>t.id===w?{...t,content:t.content+e}:t);{x(!1);let r={id:w,content:e,sender:"ai",timestamp:new Date().toISOString(),hasWebSearch:t};return[...a,r]}})},e=>{g(t=>t.map(t=>t.id===w?{...t,content:e}:t)),x(!1),D(!1),I(null),d&&saveLastUsedModelForChat(b,d),o&&o()},e=>{console.error("Erro na IA:",e),g(t=>t.map(t=>t.id===w?{...t,content:"❌ Erro: ".concat(e)}:t)),x(!1),D(!1),I(null)})},loadChatName=async e=>{if(null==c?void 0:c.email)try{let t=await getUsernameFromFirestore(),a=await (0,i.getDoc)((0,i.doc)(l.db,"usuarios",t,"conversas",e));if(a.exists()){let e=a.data(),t=e.name||"Conversa sem nome";F(t)}else F("Conversa n\xe3o encontrada")}catch(e){console.error("Erro ao carregar nome do chat:",e),F("Erro ao carregar nome")}},loadChatMessages=async e=>{if(null==c?void 0:c.email){z(!0);try{let t=await getUsernameFromFirestore(),a=await R.loadChatMessages(t,e);console.log("=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ==="),console.log("Total de mensagens:",a.length),a.forEach((e,t)=>{console.log("Mensagem ".concat(t+1,":"),{id:e.id,role:e.role,hasAttachments:!!(e.attachments&&e.attachments.length>0),attachmentsCount:e.attachments&&e.attachments.length||0,attachments:e.attachments})});let r=R.convertFromAIFormat(a);console.log("=== DEBUG: MENSAGENS CONVERTIDAS ==="),console.log("Total de mensagens convertidas:",r.length),r.forEach((e,t)=>{console.log("Mensagem convertida ".concat(t+1,":"),{id:e.id,sender:e.sender,hasAttachments:!!(e.attachments&&e.attachments.length>0),attachmentsCount:e.attachments&&e.attachments.length||0,attachments:e.attachments})}),g(r)}catch(e){console.error("Erro ao carregar mensagens do chat:",e),g([])}finally{z(!1)}}};(0,s.useEffect)(()=>{c&&!t&&loadGlobalLastUsedModel()},[c,t]),(0,s.useEffect)(()=>{t&&t!==v?(j(t),z(!0),g([]),loadChatMessages(t),loadChatName(t),loadLastUsedModelForChat(t)):!t&&v&&(j(null),g([]),F("Nova Conversa"),z(!1),loadGlobalLastUsedModel())},[t,null==c?void 0:c.email]);let handleDeleteMessage=async e=>{if(v&&(null==c?void 0:c.email)){g(t=>t.filter(t=>t.id!==e));try{let t=await getUsernameFromFirestore(),a=await R.deleteMessage(t,v,e);a||(loadChatMessages(v),console.error("Falha ao deletar mensagem no servidor"))}catch(e){loadChatMessages(v),console.error("Erro ao deletar mensagem:",e)}}},handleRegenerateMessage=async e=>{if(!v||!(null==c?void 0:c.email))return;let t=p.findIndex(t=>t.id===e);if(-1===t)return;let a=p[t],r=p.slice(0,t+1);g(r),u(a.content),x(!0),D(!0);let s=R.generateMessageId();I(s);let n=await getUsernameFromFirestore();try{for(let e=t+1;e<p.length;e++){let t=p[e];await R.deleteMessage(n,v,t.id)}await R.sendMessageSafe({username:n,chatId:v,message:a.content,model:b,isRegeneration:!0},e=>{g(t=>{let a=t.findIndex(e=>e.id===s);if(-1!==a)return t.map(t=>t.id===s?{...t,content:t.content+e}:t);{x(!1);let a={id:s,content:e,sender:"ai",timestamp:new Date().toISOString(),hasWebSearch:!1};return[...t,a]}})},e=>{g(t=>t.map(t=>t.id===s?{...t,content:e}:t)),x(!1),D(!1),I(null),u(""),o&&o()},e=>{console.error("Erro na regenera\xe7\xe3o:",e),g(t=>t.map(t=>t.id===s?{...t,content:"❌ Erro na regenera\xe7\xe3o: ".concat(e)}:t)),x(!1),D(!1),I(null),u("")})}catch(e){console.error("Erro ao regenerar mensagem:",e),x(!1),D(!1),I(null),u(""),loadChatMessages(v)}},handleEditMessage=async(e,t)=>{if(v&&(null==c?void 0:c.email)){g(a=>a.map(a=>a.id===e?{...a,content:t}:a));try{let a=await getUsernameFromFirestore(),r=await R.updateMessage(a,v,e,t);r||(loadChatMessages(v),console.error("Falha ao atualizar mensagem no servidor"))}catch(e){loadChatMessages(v),console.error("Erro ao atualizar mensagem:",e)}}},convertToChatMessages=e=>e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:new Date(e.timestamp).getTime(),isFavorite:e.isFavorite||!1,attachments:e.attachments||[]})),getAllChatAttachments=()=>{let e=[];p.forEach(t=>{t.attachments&&t.attachments.length>0&&e.push(...t.attachments)});let t=e.filter((e,t,a)=>t===a.findIndex(t=>t.id===e.id));return t},saveAttachmentStates=async e=>{if(B&&v)try{let t={id:v,name:T||"Chat",messages:e.map(e=>({id:e.id,content:e.content,role:"user"===e.sender?"user":"assistant",timestamp:e.timestamp,isFavorite:e.isFavorite,attachments:e.attachments})),createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()},a=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),r=(0,d.iH)(l.tO,"usuarios/".concat(B,"/conversas/").concat(v,"/chat.json"));await (0,d.KV)(r,a),console.log("✅ Estado dos anexos salvo no Firebase Storage"),console.log("\uD83D\uDCC1 Dados salvos:",{chatId:v,totalMessages:t.messages.length,messagesWithAttachments:t.messages.filter(e=>e.attachments&&e.attachments.length>0).length})}catch(e){console.error("❌ Erro ao salvar estado dos anexos:",e)}},getActiveAttachmentIds=()=>{let e=getAllChatAttachments();return e.filter(e=>!1!==e.isActive).map(e=>e.id)};return(0,r.jsxs)("div",{className:"flex-1 flex flex-col h-screen",children:[(0,r.jsx)(Upperbar,{currentChat:t,chatName:T,aiModel:b,onDownload:()=>{y(!0)},onAttachments:()=>{M(!0)},onStatistics:()=>{E(!0)},isLoading:h,attachmentsCount:getAllChatAttachments().length,aiMetadata:{usedCoT:!1}}),(0,r.jsx)("div",{ref:W,className:"flex-1 min-h-0",style:{height:"calc(100vh - 200px)"},children:(0,r.jsx)(ChatInterface,{messages:p,isLoading:h,isLoadingChat:P,isStreaming:L,streamingMessageId:A,onDeleteMessage:handleDeleteMessage,onRegenerateMessage:handleRegenerateMessage,onEditMessage:handleEditMessage,onCopyMessage:e=>{navigator.clipboard.writeText(e).then(()=>{console.log("Mensagem copiada para a \xe1rea de transfer\xeancia")})}})}),(0,r.jsx)(InputBar,{message:m,setMessage:u,onSendMessage:handleSendMessage,isLoading:h,selectedModel:b,onModelChange:handleModelChange,onScrollToTop:()=>{var e;let t=null===(e=W.current)||void 0===e?void 0:e.querySelector(".overflow-y-auto");t&&t.scrollTo({top:0,behavior:"smooth"})},onScrollToBottom:()=>{var e;let t=null===(e=W.current)||void 0===e?void 0:e.querySelector(".overflow-y-auto");t&&t.scrollTo({top:t.scrollHeight,behavior:"smooth"})},isStreaming:L,onCancelStreaming:()=>{R.cancelRequest(),x(!1),D(!1),I(null)},onOpenModelModal:()=>k(!0),username:B,chatId:v||void 0,activeAttachmentsCount:getActiveAttachmentIds().length}),(0,r.jsx)(dashboard_DownloadModal,{isOpen:w,onClose:()=>y(!1),messages:convertToChatMessages(p),chatName:T}),(0,r.jsx)(dashboard_ModelSelectionModal,{isOpen:N,onClose:()=>k(!1),currentModel:b,onModelSelect:handleModelChange}),(0,r.jsx)(AttachmentsModal,{isOpen:C,onClose:()=>M(!1),attachments:getAllChatAttachments(),activeAttachments:getActiveAttachmentIds(),onToggleAttachment:e=>{g(t=>{let a=t.map(t=>{if(t.attachments&&t.attachments.length>0){let a=t.attachments.map(t=>{if(t.id===e){let e=!1!==t.isActive;return{...t,isActive:!e}}return t});return{...t,attachments:a}}return t});return saveAttachmentStates(a),a})}}),(0,r.jsx)(StatisticsModal,{isOpen:S,onClose:()=>E(!1),messages:convertToChatMessages(p),chatName:T})]})}var O=a(8081);function SettingsModal(e){let{isOpen:t,onClose:a,userData:o,onUserDataUpdate:u}=e,{logout:h,user:x}=(0,n.useAuth)(),[p,g]=(0,s.useState)("geral"),[b,f]=(0,s.useState)(!1),v=(0,s.useRef)(null),[j,w]=(0,s.useState)({username:o.username,profileImage:o.profileImage||"",currentPassword:"",newPassword:"",confirmPassword:""}),[y,N]=(0,s.useState)({fonte:"Inter",tamanhoFonte:14,palavrasPorSessao:5e3}),[k,C]=(0,s.useState)([{nome:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",modeloPadrao:"meta-llama/llama-3.1-8b-instruct:free",ativo:!1},{nome:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",modeloPadrao:"deepseek-chat",ativo:!1}]),[M,S]=(0,s.useState)([]),[E,L]=(0,s.useState)([]),[D,A]=(0,s.useState)(!1),[I,T]=(0,s.useState)(!1),[F,P]=(0,s.useState)({titulo:"",conteudo:"",cor:"#3B82F6",categoria:null,chatId:null,global:!0}),[z,B]=(0,s.useState)({nome:"",descricao:"",cor:"#3B82F6"}),[R,W]=(0,s.useState)(!1),[U,H]=(0,s.useState)({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),[_,V]=(0,s.useState)(null),[q,K]=(0,s.useState)({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1});(0,s.useEffect)(()=>{let loadConfigurations=async()=>{if(o.username)try{console.log("Carregando configura\xe7\xf5es para:",o.username);let e=await (0,i.getDoc)((0,i.doc)(l.db,"usuarios",o.username,"configuracoes","settings"));if(e.exists()){let t=e.data();if(console.log("Configura\xe7\xf5es carregadas:",t),t.aparencia&&N(t.aparencia),t.endpoints){let e=Object.values(t.endpoints);C(e)}else C([{nome:"OpenRouter",url:"https://openrouter.ai/api/v1/chat/completions",apiKey:"",modeloPadrao:"meta-llama/llama-3.1-8b-instruct:free",ativo:!1},{nome:"DeepSeek",url:"https://api.deepseek.com/v1/chat/completions",apiKey:"",modeloPadrao:"deepseek-chat",ativo:!1}]);if(t.memorias){let e=Object.values(t.memorias);S(e)}if(t.categorias){let e=Object.values(t.categorias);L(e)}}else console.log("Nenhuma configura\xe7\xe3o encontrada, usando padr\xf5es"),N({fonte:"Inter",tamanhoFonte:14,palavrasPorSessao:5e3})}catch(e){console.error("Erro ao carregar configura\xe7\xf5es:",e)}};t&&o.username&&(w({username:o.username,profileImage:o.profileImage||"",currentPassword:"",newPassword:"",confirmPassword:""}),loadConfigurations())},[t,o.username,o.profileImage]);let deleteUserDocuments=async e=>{try{console.log("Iniciando exclus\xe3o de documentos para:",e);try{let t=(0,i.doc)(l.db,"usuarios",e,"configuracoes","settings"),a=await (0,i.getDoc)(t);a.exists()&&(await (0,i.oe)(t),console.log("Configura\xe7\xf5es deletadas"))}catch(e){console.log("Erro ao deletar configura\xe7\xf5es:",e)}try{let t=(0,i.collection)(l.db,"usuarios",e,"chats"),a=await (0,i.getDocs)(t),r=a.docs.map(e=>(0,i.oe)(e.ref));await Promise.all(r),console.log("Chats deletados")}catch(e){console.log("Erro ao deletar chats:",e)}let t=(0,i.doc)(l.db,"usuarios",e);await (0,i.oe)(t),console.log("Documento principal do usu\xe1rio deletado")}catch(e){throw console.error("Erro ao deletar documentos do usu\xe1rio:",e),e}},updateUsername=async function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!o.username||!e||e===o.username)return t&&alert("Nome de usu\xe1rio inv\xe1lido ou igual ao atual."),!1;if(e.length<3)return t&&alert("Nome de usu\xe1rio deve ter pelo menos 3 caracteres."),!1;let a=!1;try{console.log("Atualizando username de",o.username,"para",e);let r=await (0,i.getDoc)((0,i.doc)(l.db,"usuarios",e));if(r.exists())return t&&alert("Este nome de usu\xe1rio j\xe1 est\xe1 em uso. Escolha outro."),!1;let s=(0,i.doc)(l.db,"usuarios",o.username),n=await (0,i.getDoc)(s);if(!n.exists())return t&&alert("Usu\xe1rio n\xe3o encontrado."),!1;let d=n.data();await (0,i.pl)((0,i.doc)(l.db,"usuarios",e),{...d,username:e,updatedAt:new Date().toISOString()}),a=!0,console.log("Novo documento criado para:",e);try{let t=await (0,i.getDoc)((0,i.doc)(l.db,"usuarios",o.username,"configuracoes","settings"));t.exists()&&(await (0,i.pl)((0,i.doc)(l.db,"usuarios",e,"configuracoes","settings"),t.data()),console.log("Configura\xe7\xf5es copiadas para novo username"));try{let t=(0,i.collection)(l.db,"usuarios",o.username,"chats"),a=await (0,i.getDocs)(t);for(let t of a.docs){let a=t.data();await (0,i.pl)((0,i.doc)(l.db,"usuarios",e,"chats",t.id),a)}a.docs.length>0&&console.log("".concat(a.docs.length," chats copiados para novo username"))}catch(e){console.log("Erro ao copiar chats:",e)}}catch(e){console.log("Erro ao copiar dados:",e)}return await deleteUserDocuments(o.username),console.log("Todos os documentos do usu\xe1rio antigo foram deletados"),u({...o,username:e}),t&&alert("Nome de usu\xe1rio atualizado com sucesso!"),!0}catch(r){if(console.error("Erro ao atualizar username:",r),a)try{await (0,i.oe)((0,i.doc)(l.db,"usuarios",e)),console.log("Rollback realizado - novo usu\xe1rio deletado")}catch(e){console.error("Erro no rollback:",e)}return t&&alert("Erro ao atualizar nome de usu\xe1rio: ".concat(r instanceof Error?r.message:"Erro desconhecido")),!1}},saveConfigurations=async()=>{if(!o.username){alert("Erro: usu\xe1rio n\xe3o identificado");return}try{if(f(!0),j.username!==o.username){let e=await updateUsername(j.username,!1);if(!e)return}let e=j.username!==o.username?j.username:o.username,t={aparencia:{fonte:y.fonte,tamanhoFonte:y.tamanhoFonte,palavrasPorSessao:y.palavrasPorSessao},endpoints:{},memorias:{},categorias:{},updatedAt:new Date().toISOString()};k.forEach((e,a)=>{t.endpoints[e.nome||"endpoint_".concat(a)]=e}),M.forEach((e,a)=>{t.memorias["memoria_".concat(a)]=e}),E.forEach((e,a)=>{t.categorias[e.nome||"categoria_".concat(a)]=e}),console.log("Salvando configura\xe7\xf5es para:",e),console.log("Dados a serem salvos:",t);let a=(0,i.doc)(l.db,"usuarios",e,"configuracoes","settings");await (0,i.pl)(a,t),console.log("Configura\xe7\xf5es salvas com sucesso no Firestore"),alert("Configura\xe7\xf5es salvas com sucesso!")}catch(e){console.error("Erro ao salvar configura\xe7\xf5es:",e),alert("Erro ao salvar configura\xe7\xf5es: ".concat(e instanceof Error?e.message:"Erro desconhecido"))}finally{f(!1)}};if(!t)return null;let handleProfileImageUpload=async e=>{if(x)try{f(!0);let t=(0,d.iH)(l.tO,"usuarios/".concat(o.username,"/profile.jpg"));await (0,d.KV)(t,e);let a=await (0,d.Jt)(t);w(e=>({...e,profileImage:a}));let r=(0,i.doc)(l.db,"usuarios",o.username);await (0,i.r7)(r,{profileImage:a}),u({...o,profileImage:a}),alert("Foto de perfil atualizada com sucesso!")}catch(e){console.error("Erro ao fazer upload da imagem:",e),alert("Erro ao atualizar foto de perfil.")}finally{f(!1)}},handlePasswordChange=async()=>{if(!x||!j.currentPassword||!j.newPassword){alert("Preencha todos os campos de senha.");return}if(j.newPassword!==j.confirmPassword){alert("As senhas n\xe3o coincidem.");return}try{f(!0);let e=O.w9.credential(x.email,j.currentPassword);await (0,O.aF)(x,e),await (0,O.gQ)(x,j.newPassword),w(e=>({...e,currentPassword:"",newPassword:"",confirmPassword:""})),alert("Senha alterada com sucesso!")}catch(e){console.error("Erro ao alterar senha:",e),alert("Erro ao alterar senha. Verifique a senha atual.")}finally{f(!1)}},handleLogout=async()=>{confirm("Tem certeza que deseja sair?")&&(await h(),a())},handleToggleEndpoint=e=>{C(t=>t.map((t,a)=>a===e?{...t,ativo:!t.ativo}:t))},handleDeleteEndpoint=e=>{confirm("Tem certeza que deseja deletar este endpoint?")&&C(t=>t.filter((t,a)=>a!==e))},handleEditEndpoint=e=>{let t=k[e];K({...t}),V(e)},handleSaveEditEndpoint=()=>{if(null!==_){if(!q.apiKey||!q.modeloPadrao){alert("API Key e Modelo Padr\xe3o s\xe3o obrigat\xf3rios.");return}C(e=>e.map((e,t)=>t===_?{...q}:e)),V(null),K({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),alert("Endpoint atualizado com sucesso!")}},handleCancelEditEndpoint=()=>{V(null),K({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1})},handleTestEndpoint=async e=>{if(!e.apiKey){alert("API Key \xe9 necess\xe1ria para testar o endpoint.");return}try{f(!0);let t=await fetch(e.url,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e.apiKey)},body:JSON.stringify({model:e.modeloPadrao||"gpt-3.5-turbo",messages:[{role:"user",content:"Test message"}],max_tokens:10})});t.ok?alert("✅ Endpoint testado com sucesso!"):alert("❌ Erro ao testar endpoint. Verifique as configura\xe7\xf5es.")}catch(e){console.error("Erro ao testar endpoint:",e),alert("❌ Erro ao conectar com o endpoint.")}finally{f(!1)}},handleDeleteMemory=e=>{confirm("Tem certeza que deseja deletar esta mem\xf3ria?")&&S(t=>t.filter((t,a)=>a!==e))},handleDeleteCategory=e=>{confirm("Tem certeza que deseja deletar esta categoria?")&&L(t=>t.filter((t,a)=>a!==e))},G=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#06B6D4","#84CC16"];return(0,r.jsx)(m.M,{children:t&&(0,r.jsx)(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)(c.E.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 lg:p-6 border-b border-white/20",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl lg:text-2xl font-bold text-white",children:"Configura\xe7\xf5es"}),(0,r.jsxs)("p",{className:"text-white/60 text-sm lg:hidden mt-1",children:["geral"===p&&"Informa\xe7\xf5es pessoais e senha","aparencia"===p&&"Personaliza\xe7\xe3o da interface","ia"===p&&"Endpoints de intelig\xeancia artificial","memoria"===p&&"Sistema de mem\xf3rias"]})]}),(0,r.jsx)("button",{onClick:a,className:"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,r.jsx)("div",{className:"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10",children:(0,r.jsx)("nav",{className:"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible",children:(0,r.jsxs)("div",{className:"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0",children:[(0,r.jsxs)("button",{onClick:()=>g("geral"),className:"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ".concat("geral"===p?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"),children:[(0,r.jsx)("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),(0,r.jsx)("span",{className:"font-medium text-sm lg:text-base",children:"Geral"})]}),(0,r.jsxs)("button",{onClick:()=>g("aparencia"),className:"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ".concat("aparencia"===p?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"),children:[(0,r.jsx)("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"})}),(0,r.jsx)("span",{className:"font-medium text-sm lg:text-base",children:"Apar\xeancia"})]}),(0,r.jsxs)("button",{onClick:()=>g("ia"),className:"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ".concat("ia"===p?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"),children:[(0,r.jsx)("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,r.jsx)("span",{className:"font-medium text-sm lg:text-base",children:"IA"})]}),(0,r.jsxs)("button",{onClick:()=>g("memoria"),className:"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap ".concat("memoria"===p?"bg-blue-600 text-white shadow-lg":"text-white/70 hover:text-white hover:bg-white/10"),children:[(0,r.jsx)("svg",{className:"w-4 lg:w-5 h-4 lg:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,r.jsx)("span",{className:"font-medium text-sm lg:text-base",children:"Mem\xf3ria"})]})]})})}),(0,r.jsx)("div",{className:"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]",children:(0,r.jsxs)(m.M,{mode:"wait",children:["geral"===p&&(0,r.jsx)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Configura\xe7\xf5es Gerais"}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Foto de Perfil"}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)("div",{className:"relative",children:j.profileImage?(0,r.jsx)("img",{src:j.profileImage,alt:"Profile",className:"w-20 h-20 rounded-full object-cover border-2 border-white/20"}):(0,r.jsx)("div",{className:"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:o.username.charAt(0).toUpperCase()})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("button",{onClick:()=>{var e;return null===(e=v.current)||void 0===e?void 0:e.click()},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium",children:"Alterar Foto"}),(0,r.jsx)("p",{className:"text-white/60 text-sm mt-2",children:"JPG, PNG ou GIF. M\xe1ximo 5MB."})]})]}),(0,r.jsx)("input",{ref:v,type:"file",accept:"image/*",onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];a&&handleProfileImageUpload(a)},className:"hidden"})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nome de Usu\xe1rio"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("input",{type:"text",value:j.username,onChange:e=>w(t=>({...t,username:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite seu nome de usu\xe1rio"}),j.username!==o.username&&(0,r.jsx)("div",{className:"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-yellow-300 text-sm",children:'⚠️ Nome de usu\xe1rio alterado. Clique em "Salvar Configura\xe7\xf5es" para aplicar as mudan\xe7as.'})})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Alterar Senha"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Senha Atual"}),(0,r.jsx)("input",{type:"password",value:j.currentPassword,onChange:e=>w(t=>({...t,currentPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite sua senha atual"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nova Senha"}),(0,r.jsx)("input",{type:"password",value:j.newPassword,onChange:e=>w(t=>({...t,newPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Digite sua nova senha"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Confirmar Nova Senha"}),(0,r.jsx)("input",{type:"password",value:j.confirmPassword,onChange:e=>w(t=>({...t,confirmPassword:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirme sua nova senha"})]}),(0,r.jsx)("button",{onClick:handlePasswordChange,disabled:b,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:b?"Alterando...":"Alterar Senha"})]})]})]})},"geral"),"aparencia"===p&&(0,r.jsx)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Configura\xe7\xf5es de Apar\xeancia"}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Fonte do Chat"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Fam\xedlia da Fonte"}),(0,r.jsxs)("select",{value:y.fonte,onChange:e=>N(t=>({...t,fonte:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"Inter",className:"bg-gray-800",children:"Inter"}),(0,r.jsx)("option",{value:"Roboto",className:"bg-gray-800",children:"Roboto"}),(0,r.jsx)("option",{value:"JetBrains Mono",className:"bg-gray-800",children:"JetBrains Mono"}),(0,r.jsx)("option",{value:"Lato",className:"bg-gray-800",children:"Lato"}),(0,r.jsx)("option",{value:"Fira Code",className:"bg-gray-800",children:"Fira Code"}),(0,r.jsx)("option",{value:"Merriweather",className:"bg-gray-800",children:"Merriweather"}),(0,r.jsx)("option",{value:"Open Sans",className:"bg-gray-800",children:"Open Sans"}),(0,r.jsx)("option",{value:"Source Sans Pro",className:"bg-gray-800",children:"Source Sans Pro"}),(0,r.jsx)("option",{value:"Poppins",className:"bg-gray-800",children:"Poppins"}),(0,r.jsx)("option",{value:"Nunito",className:"bg-gray-800",children:"Nunito"})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-white/80 text-sm mb-2",children:"Pr\xe9-visualiza\xe7\xe3o:"}),(0,r.jsx)("div",{className:"text-white p-3 bg-white/5 rounded border border-white/10",style:{fontFamily:y.fonte,fontSize:"".concat(y.tamanhoFonte,"px")},children:"Esta \xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit."})]})]})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Tamanho da Fonte"}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:["Tamanho: ",y.tamanhoFonte,"px"]}),(0,r.jsx)("input",{type:"range",min:"10",max:"24",value:y.tamanhoFonte,onChange:e=>N(t=>({...t,tamanhoFonte:parseInt(e.target.value)})),className:"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[(0,r.jsx)("span",{children:"10px"}),(0,r.jsx)("span",{children:"24px"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Sess\xf5es de Chat"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"text-white font-medium",children:"Divis\xe3o Autom\xe1tica"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Dividir chats longos em sess\xf5es baseadas na contagem de palavras"})]}),(0,r.jsx)("button",{className:"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors",children:(0,r.jsx)("span",{className:"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:["Palavras por Sess\xe3o: ",y.palavrasPorSessao.toLocaleString()]}),(0,r.jsx)("input",{type:"range",min:"1000",max:"20000",step:"500",value:y.palavrasPorSessao,onChange:e=>N(t=>({...t,palavrasPorSessao:parseInt(e.target.value)})),className:"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[(0,r.jsx)("span",{children:"1.000"}),(0,r.jsx)("span",{children:"20.000"})]})]}),(0,r.jsx)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3",children:(0,r.jsxs)("p",{className:"text-blue-300 text-sm",children:["\uD83D\uDCA1 ",(0,r.jsx)("strong",{children:"Dica:"})," Sess\xf5es menores carregam mais r\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\xeancia."]})})]})]})]})},"aparencia"),"ia"===p&&(0,r.jsx)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Intelig\xeancia Artificial"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("p",{className:"text-white/80",children:"Gerencie seus endpoints de IA personalizados"}),(0,r.jsxs)("button",{onClick:()=>W(!R),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{children:"Adicionar Endpoint"})]})]}),R&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Novo Endpoint"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nome do Endpoint *"}),(0,r.jsx)("input",{type:"text",value:U.nome,onChange:e=>H(t=>({...t,nome:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Meu Endpoint"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"URL do Endpoint *"}),(0,r.jsx)("input",{type:"url",value:U.url,onChange:e=>H(t=>({...t,url:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"https://api.exemplo.com/v1/chat/completions"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"API Key *"}),(0,r.jsx)("input",{type:"password",value:U.apiKey,onChange:e=>H(t=>({...t,apiKey:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"sk-..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Modelo Padr\xe3o"}),(0,r.jsx)("input",{type:"text",value:U.modeloPadrao,onChange:e=>H(t=>({...t,modeloPadrao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"gpt-3.5-turbo"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>W(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),(0,r.jsx)("button",{onClick:()=>{if(!U.nome||!U.url||!U.apiKey){alert("Preencha todos os campos obrigat\xf3rios.");return}C(e=>[...e,{...U}]),H({nome:"",url:"",apiKey:"",modeloPadrao:"",ativo:!1}),W(!1),alert("Endpoint adicionado com sucesso!")},className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Adicionar"})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:k.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.ativo?"bg-green-500":"bg-gray-500")}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-white",children:e.nome}),("OpenRouter"===e.nome||"DeepSeek"===e.nome)&&(0,r.jsx)("span",{className:"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full",children:"Pr\xe9-configurado"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>handleToggleEndpoint(t),className:"px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e.ativo?"bg-green-600 hover:bg-green-700 text-white":"bg-gray-600 hover:bg-gray-700 text-white"),children:e.ativo?"Ativo":"Inativo"}),(0,r.jsx)("button",{onClick:()=>handleTestEndpoint(e),disabled:b||!e.apiKey,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Testar"}),(0,r.jsx)("button",{onClick:()=>handleEditEndpoint(t),className:"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Editar"}),"OpenRouter"!==e.nome&&"DeepSeek"!==e.nome&&(0,r.jsx)("button",{onClick:()=>handleDeleteEndpoint(t),className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200",children:"Deletar"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white/60",children:"URL:"}),(0,r.jsx)("p",{className:"text-white font-mono text-xs break-all",children:e.url})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white/60",children:"Modelo:"}),(0,r.jsx)("p",{className:"text-white",children:e.modeloPadrao||"N\xe3o especificado"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white/60",children:"API Key:"}),(0,r.jsx)("p",{className:"text-white font-mono text-xs",children:e.apiKey?"••••••••••••"+e.apiKey.slice(-4):"N\xe3o configurada"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white/60",children:"Status:"}),(0,r.jsx)("p",{className:"font-medium ".concat(e.ativo?"text-green-400":"text-gray-400"),children:e.ativo?"Ativo":"Inativo"})]})]}),_===t&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-4 pt-4 border-t border-white/10",children:[(0,r.jsx)("h5",{className:"text-white font-semibold mb-4",children:"Editar Endpoint"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"API Key *"}),(0,r.jsx)("input",{type:"password",value:q.apiKey,onChange:e=>K(t=>({...t,apiKey:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",placeholder:"Cole sua API Key aqui..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Modelo Padr\xe3o *"}),(0,r.jsx)("input",{type:"text",value:q.modeloPadrao,onChange:e=>K(t=>({...t,modeloPadrao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",placeholder:"Ex: gpt-4, claude-3-sonnet, etc."})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 mt-4",children:[(0,r.jsx)("button",{onClick:handleCancelEditEndpoint,className:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Cancelar"}),(0,r.jsx)("button",{onClick:handleSaveEditEndpoint,className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Salvar"})]})]}),("OpenRouter"===e.nome||"DeepSeek"===e.nome)&&!e.apiKey&&_!==t&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-white/10",children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Configure sua API Key:"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"password",placeholder:"Cole sua API Key aqui...",className:"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",onChange:e=>{let a=e.target.value;C(e=>e.map((e,r)=>r===t?{...e,apiKey:a}:e))}}),(0,r.jsx)("button",{onClick:()=>handleToggleEndpoint(t),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",children:"Salvar"})]})]})]},t))})]})},"ia"),"memoria"===p&&(0,r.jsx)(c.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-8",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Sistema de Mem\xf3ria"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,r.jsxs)("button",{onClick:()=>T(!I),className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})}),(0,r.jsx)("span",{children:"Nova Categoria"})]}),(0,r.jsxs)("button",{onClick:()=>A(!D),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,r.jsx)("span",{children:"Nova Mem\xf3ria"})]})]}),I&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nova Categoria"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Nome da Categoria *"}),(0,r.jsx)("input",{type:"text",value:z.nome,onChange:e=>B(t=>({...t,nome:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Trabalho, Pessoal, Projetos..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Descri\xe7\xe3o"}),(0,r.jsx)("textarea",{value:z.descricao,onChange:e=>B(t=>({...t,descricao:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:3,placeholder:"Descreva o prop\xf3sito desta categoria..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Cor da Categoria"}),(0,r.jsx)("div",{className:"flex space-x-2",children:G.map(e=>(0,r.jsx)("button",{onClick:()=>B(t=>({...t,cor:e})),className:"w-8 h-8 rounded-full border-2 transition-all duration-200 ".concat(z.cor===e?"border-white scale-110":"border-white/30"),style:{backgroundColor:e}},e))})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>T(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),(0,r.jsx)("button",{onClick:()=>{if(!z.nome){alert("Nome da categoria \xe9 obrigat\xf3rio.");return}L(e=>[...e,{...z}]),B({nome:"",descricao:"",cor:"#3B82F6"}),T(!1),alert("Categoria criada com sucesso!")},className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Criar Categoria"})]})]}),D&&(0,r.jsxs)(c.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 border border-white/10 rounded-xl p-6 mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Nova Mem\xf3ria"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"T\xedtulo da Mem\xf3ria *"}),(0,r.jsx)("input",{type:"text",value:F.titulo,onChange:e=>P(t=>({...t,titulo:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ex: Informa\xe7\xf5es importantes sobre..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Conte\xfado *"}),(0,r.jsx)("textarea",{value:F.conteudo,onChange:e=>P(t=>({...t,conteudo:e.target.value})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Digite o conte\xfado da mem\xf3ria..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Categoria"}),(0,r.jsxs)("select",{value:F.categoria||"",onChange:e=>P(t=>({...t,categoria:e.target.value||null})),className:"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",className:"bg-gray-800",children:"Sem categoria"}),E.map((e,t)=>(0,r.jsx)("option",{value:e.nome,className:"bg-gray-800",children:e.nome},t))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white/80 text-sm font-medium mb-2",children:"Cor da Mem\xf3ria"}),(0,r.jsx)("div",{className:"flex space-x-2",children:G.slice(0,4).map(e=>(0,r.jsx)("button",{onClick:()=>P(t=>({...t,cor:e})),className:"w-8 h-8 rounded-full border-2 transition-all duration-200 ".concat(F.cor===e?"border-white scale-110":"border-white/30"),style:{backgroundColor:e}},e))})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:F.global,onChange:e=>P(t=>({...t,global:e.target.checked})),className:"w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2"}),(0,r.jsx)("span",{className:"text-white/80 text-sm",children:"Mem\xf3ria Global"})]}),(0,r.jsx)("p",{className:"text-white/50 text-xs",children:"Mem\xf3rias globais ficam dispon\xedveis em todos os chats"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-4",children:[(0,r.jsx)("button",{onClick:()=>A(!1),className:"px-4 py-2 text-white/70 hover:text-white transition-colors",children:"Cancelar"}),(0,r.jsx)("button",{onClick:()=>{if(!F.titulo||!F.conteudo){alert("T\xedtulo e conte\xfado s\xe3o obrigat\xf3rios.");return}S(e=>[...e,{...F}]),P({titulo:"",conteudo:"",cor:"#3B82F6",categoria:null,chatId:null,global:!0}),A(!1),alert("Mem\xf3ria criada com sucesso!")},className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:"Criar Mem\xf3ria"})]})]}),E.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Categorias"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:E.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.cor}}),(0,r.jsx)("h5",{className:"text-white font-medium",children:e.nome})]}),(0,r.jsx)("button",{onClick:()=>handleDeleteCategory(t),className:"text-red-400 hover:text-red-300 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),e.descricao&&(0,r.jsx)("p",{className:"text-white/60 text-sm",children:e.descricao})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-white mb-4",children:["Mem\xf3rias (",M.length,")"]}),0===M.length?(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-8 text-center",children:[(0,r.jsx)("svg",{className:"w-12 h-12 text-white/40 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,r.jsx)("p",{className:"text-white/60",children:"Nenhuma mem\xf3ria criada ainda"}),(0,r.jsx)("p",{className:"text-white/40 text-sm mt-1",children:'Clique em "Nova Mem\xf3ria" para come\xe7ar'})]}):(0,r.jsx)("div",{className:"space-y-4",children:M.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white/5 border border-white/10 rounded-xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full flex-shrink-0",style:{backgroundColor:e.cor}}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h5",{className:"text-white font-semibold",children:e.titulo}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[e.categoria&&(0,r.jsx)("span",{className:"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full",children:e.categoria}),(0,r.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(e.global?"bg-green-500/20 text-green-300":"bg-orange-500/20 text-orange-300"),children:e.global?"Global":"Chat Espec\xedfico"})]})]})]}),(0,r.jsx)("button",{onClick:()=>handleDeleteMemory(t),className:"text-red-400 hover:text-red-300 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),(0,r.jsx)("p",{className:"text-white/80 text-sm leading-relaxed",children:e.conteudo})]},t))})]})]})},"memoria")]})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-white/20",children:[(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsx)("button",{onClick:handleLogout,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium",children:"Sair da Conta"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("button",{onClick:a,className:"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium",children:"Cancelar"}),(0,r.jsx)("button",{onClick:saveConfigurations,disabled:b,className:"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium",children:b?"Salvando...":"Salvar Configura\xe7\xf5es"})]})]})]})})})}function Dashboard(){let{user:e,loading:t}=(0,n.useAuth)(),i=(0,o.useRouter)(),[d,c]=(0,s.useState)(null),[m,u]=(0,s.useState)(!0),[x,p]=(0,s.useState)(!1),[g,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)(!1),[j,w]=(0,s.useState)(null),y=(0,s.useRef)(null);return((0,s.useEffect)(()=>{t||e||i.push("/login")},[e,t,i]),(0,s.useEffect)(()=>{let fetchUserData=async()=>{var t,r,s;if(e)try{let{collection:s,query:n,where:o,getDocs:i}=await Promise.resolve().then(a.bind(a,4086)),d=s(l.db,"usuarios"),m=n(d,o("email","==",e.email)),u=await i(m);if(u.empty)c({username:(null===(r=e.email)||void 0===r?void 0:r.split("@")[0])||"Usu\xe1rio",email:e.email||"",balance:0,createdAt:new Date().toISOString()});else{let a=u.docs[0],r=a.data();c({username:r.username||(null===(t=e.email)||void 0===t?void 0:t.split("@")[0])||"Usu\xe1rio",email:r.email||e.email||"",balance:r.balance||0,createdAt:r.createdAt||new Date().toISOString()})}}catch(t){console.error("Erro ao buscar dados do usu\xe1rio:",t),c({username:(null===(s=e.email)||void 0===s?void 0:s.split("@")[0])||"Usu\xe1rio",email:e.email||"",balance:0,createdAt:new Date().toISOString()})}finally{u(!1)}};e&&!t&&fetchUserData()},[e,t]),t||m)?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-rafthor flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white text-xl",children:"Carregando..."})}):e&&d?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-rafthor flex",children:[(0,r.jsx)(h,{ref:y,userData:d,isOpen:x,isCollapsed:g,onToggle:()=>{window.innerWidth<1024?p(!x):b(!g)},onSettingsOpen:()=>v(!0),onChatSelect:w,currentChat:j,showCloseButton:!0}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col h-screen overflow-hidden transition-all duration-300 ".concat(g?"lg:ml-0":"lg:ml-80"),children:[(0,r.jsx)("div",{className:"lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4",children:(0,r.jsx)("button",{onClick:()=>p(!0),className:"text-white hover:text-white/80 transition-colors",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,r.jsx)(ChatArea,{currentChat:j,onChatCreated:e=>{var t;w(e),null===(t=y.current)||void 0===t||t.reloadChats()},onUpdateOpenRouterBalance:()=>{y.current&&y.current.updateOpenRouterBalance()}})]}),(0,r.jsx)(SettingsModal,{isOpen:f,onClose:()=>v(!1),userData:d,onUserDataUpdate:c}),x&&(0,r.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black/50 z-40",onClick:()=>p(!1)})]}):null}},3549:function(e,t,a){"use strict";a.r(t),a.d(t,{AuthProvider:function(){return AuthProvider},useAuth:function(){return useAuth}});var r=a(7437),s=a(2265),n=a(8081),o=a(6831);let l=(0,s.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,s.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=e=>{let{children:t}=e,[a,i]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,n.Aj)(o.I8,e=>{i(e),c(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,n.w7)(o.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return(0,r.jsx)(l.Provider,{value:{user:a,loading:d,logout},children:t})}},6831:function(e,t,a){"use strict";a.d(t,{I8:function(){return c},db:function(){return m},tO:function(){return u}});var r=a(994),s=a(8081),n=a(4086),o=a(5813),l=a(3216);let i={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!i.apiKey||i.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let d=(0,r.ZF)(i),c=(0,s.v0)(d),m=(0,n.ad)(d),u=(0,o.cF)(d);(0,l.$C)(d)},8929:function(){},576:function(){}},function(e){e.O(0,[609,15,954,14,933,971,472,744],function(){return e(e.s=6861)}),_N_E=e.O()}]);