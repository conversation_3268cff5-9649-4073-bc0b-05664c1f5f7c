(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{9660:function(e,t,r){Promise.resolve().then(r.t.bind(r,2998,23)),Promise.resolve().then(r.t.bind(r,2489,23)),Promise.resolve().then(r.bind(r,3549))},3549:function(e,t,r){"use strict";r.r(t),r.d(t,{AuthProvider:function(){return AuthProvider},useAuth:function(){return useAuth}});var n=r(7437),o=r(2265),u=r(8081),a=r(6831);let i=(0,o.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,o.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=e=>{let{children:t}=e,[r,s]=(0,o.useState)(null),[c,l]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=(0,u.Aj)(a.I8,e=>{s(e),l(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,u.w7)(a.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return(0,n.jsx)(i.Provider,{value:{user:r,loading:c,logout},children:t})}},6831:function(e,t,r){"use strict";r.d(t,{I8:function(){return l},db:function(){return f},tO:function(){return h}});var n=r(994),o=r(8081),u=r(4086),a=r(5813),i=r(3216);let s={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!s.apiKey||s.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let c=(0,n.ZF)(s),l=(0,o.v0)(c),f=(0,u.ad)(c),h=(0,a.cF)(c);(0,i.$C)(c)},2489:function(){},2998:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[609,15,14,971,472,744],function(){return e(e.s=9660)}),_N_E=e.O()}]);