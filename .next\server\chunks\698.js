exports.id=698,exports.ids=[698],exports.modules={88226:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13724,23)),Promise.resolve().then(r.t.bind(r,35365,23)),Promise.resolve().then(r.t.bind(r,44900,23)),Promise.resolve().then(r.t.bind(r,44714,23)),Promise.resolve().then(r.t.bind(r,45392,23)),Promise.resolve().then(r.t.bind(r,8898,23))},2816:(e,t,r)=>{Promise.resolve().then(r.bind(r,78157))},78157:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>AuthProvider,useAuth:()=>useAuth});var o=r(30784),a=r(9885),s=r(11766),i=r(72373);let l=(0,a.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,a.useContext)(l);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=({children:e})=>{let[t,r]=(0,a.useState)(null),[n,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,s.Aj)(i.I8,e=>{r(e),u(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,s.w7)(i.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return o.jsx(l.Provider,{value:{user:t,loading:n,logout},children:e})}},72373:(e,t,r)=>{"use strict";r.d(t,{I8:()=>d,db:()=>h,tO:()=>c});var o=r(72856),a=r(11766),s=r(29904),i=r(31640),l=r(38635);let n={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!n.apiKey||n.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let u=(0,o.ZF)(n),d=(0,a.v0)(u),h=(0,s.ad)(u),c=(0,i.cF)(u);(0,l.$C)(u)},42540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>RootLayout,metadata:()=>h});var o=r(4656),a=r(40186),s=r.n(a);r(5023);var i=r(95153);let l=(0,i.createProxy)(String.raw`C:\Users\<USER>\Desktop\SiteRafthor\rafthor\src\contexts\AuthContext.tsx`),{__esModule:n,$$typeof:u}=l;l.default,l.useAuth;let d=l.AuthProvider,h={title:"Rafthor - AI Chatbot Platform",description:"Uma plataforma de chatbot com m\xfaltiplas IAs"};function RootLayout({children:e}){return o.jsx("html",{lang:"pt-BR",children:o.jsx("body",{className:s().className,children:o.jsx(d,{children:e})})})}},5023:()=>{}};