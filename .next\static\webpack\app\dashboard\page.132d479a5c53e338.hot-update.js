"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/ChatInterface.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\");\n/* harmony import */ var _AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AttachmentDisplay */ \"(app-pages-browser)/./src/components/dashboard/AttachmentDisplay.tsx\");\n/* harmony import */ var _DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteMessageModal */ \"(app-pages-browser)/./src/components/dashboard/DeleteMessageModal.tsx\");\n/* harmony import */ var _RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RegenerateMessageModal */ \"(app-pages-browser)/./src/components/dashboard/RegenerateMessageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, isLoading, isLoadingChat, onDeleteMessage, onRegenerateMessage, onEditMessage, onCopyMessage } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [animatedMessages, setAnimatedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [deleteModal, setDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        isDeleting: false\n    });\n    const [regenerateModal, setRegenerateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        messagesAffectedCount: 0,\n        isRegenerating: false\n    });\n    // Controlar animações das mensagens para evitar re-animação durante streaming\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Só animar quando realmente há novas mensagens (não atualizações de conteúdo)\n        if (messages.length > prevMessageCountRef.current) {\n            const newMessages = messages.slice(prevMessageCountRef.current);\n            const newMessageIds = new Set(newMessages.map((msg)=>msg.id));\n            setAnimatedMessages((prev)=>new Set([\n                    ...prev,\n                    ...newMessageIds\n                ]));\n            // Remover animação após completar para evitar acúmulo de memória\n            setTimeout(()=>{\n                setAnimatedMessages((prev)=>{\n                    const updated = new Set(prev);\n                    newMessageIds.forEach((id)=>updated.delete(id));\n                    return updated;\n                });\n            }, 400); // Duração da animação (300ms) + buffer\n        }\n        prevMessageCountRef.current = messages.length;\n    }, [\n        messages.length\n    ]);\n    const scrollToBottom = ()=>{\n        // Usar requestAnimationFrame para otimizar o scroll\n        requestAnimationFrame(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Debounce o scroll para evitar múltiplas chamadas\n        const timeoutId = setTimeout(()=>{\n            scrollToBottom();\n        }, 100);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        messages\n    ]);\n    const formatTime = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString(\"pt-BR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleStartEdit = (message)=>{\n        setEditingMessageId(message.id);\n        setEditingContent(message.content);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingMessageId && editingContent.trim() !== \"\") {\n            onEditMessage(editingMessageId, editingContent.trim());\n        }\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    const handleCancelEdit = ()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    const handleDeleteClick = (message)=>{\n        setDeleteModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        setDeleteModal((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await onDeleteMessage(deleteModal.messageId);\n            setDeleteModal({\n                isOpen: false,\n                messageId: \"\",\n                messageContent: \"\",\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            setDeleteModal((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        setDeleteModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            isDeleting: false\n        });\n    };\n    const handleRegenerateClick = (message)=>{\n        // Encontrar o índice da mensagem atual\n        const messageIndex = messages.findIndex((msg)=>msg.id === message.id);\n        // Contar quantas mensagens vêm depois desta (apenas as posteriores serão removidas)\n        const messagesAfterCount = messages.length - messageIndex - 1;\n        setRegenerateModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            messagesAffectedCount: messagesAfterCount,\n            isRegenerating: false\n        });\n    };\n    const handleRegenerateConfirm = async ()=>{\n        // Fechar o modal imediatamente\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n        try {\n            // Iniciar a regeneração (não aguardar conclusão)\n            onRegenerateMessage(regenerateModal.messageId);\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n        }\n    };\n    const handleRegenerateCancel = ()=>{\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n    };\n    const MessageActions = (param)=>/*#__PURE__*/ {\n        let { message, isUser } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleDeleteClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Excluir mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 7\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleRegenerateClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Regenerar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleStartEdit(message),\n                    className: \"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Editar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCopyMessage(message.content),\n                    className: \"p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Copiar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n            lineNumber: 223,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n        style: {\n            maxHeight: \"100%\"\n        },\n        children: [\n            isLoadingChat ? // Estado de carregamento de chat\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Carregando mensagens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Aguarde enquanto carregamos o hist\\xf3rico da conversa...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this) : messages.length === 0 ? // Área vazia quando não há mensagens - centralizada e mais bonita\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Comece uma nova conversa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Digite sua mensagem abaixo para come\\xe7ar a conversar com a IA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 grid grid-cols-1 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-blue-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Fa\\xe7a perguntas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Tire d\\xfavidas sobre qualquer assunto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-cyan-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Pe\\xe7a ajuda com c\\xf3digo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Programa\\xe7\\xe3o, debugging e explica\\xe7\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-purple-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Crie conte\\xfado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Textos, resumos e ideias criativas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this) : // Container das mensagens com altura controlada\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 min-h-0\",\n                children: [\n                    // Mensagens do chat\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-2 group \".concat(animatedMessages.has(message.id) ? \"animate-message-slide-in\" : \"\", \" \").concat(message.sender === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 \".concat(message.sender === \"user\" ? \"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30\" : \"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                        children: message.sender === \"user\" ? \"U\" : \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm rounded-2xl p-3 max-w-3xl border \".concat(message.sender === \"user\" ? \"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto\" : \"bg-blue-600/20 border-blue-500/20 rounded-tl-md\"),\n                                            children: editingMessageId === message.id ? // Campo de edição inline\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: editingContent,\n                                                        onChange: (e)=>setEditingContent(e.target.value),\n                                                        className: \"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                                                        placeholder: \"Digite sua mensagem...\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleCancelEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200\",\n                                                                children: \"Cancelar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSaveEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200\",\n                                                                children: \"Salvar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    message.attachments && message.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        attachments: message.attachments,\n                                                        isUserMessage: message.sender === \"user\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        content: message.content,\n                                                        hasWebSearch: message.hasWebSearch,\n                                                        webSearchAnnotations: message.webSearchAnnotations\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mt-2 \".concat(message.sender === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                            children: message.sender === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-2 animate-message-slide-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                    children: \"AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.4s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm animate-pulse\",\n                                                children: \"Digitando...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef,\n                        className: \"h-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 382,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: deleteModal.isOpen,\n                onClose: handleDeleteCancel,\n                onConfirm: handleDeleteConfirm,\n                messagePreview: deleteModal.messageContent,\n                isDeleting: deleteModal.isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: regenerateModal.isOpen,\n                onClose: handleRegenerateCancel,\n                onConfirm: handleRegenerateConfirm,\n                messagePreview: regenerateModal.messageContent,\n                messagesAffectedCount: regenerateModal.messagesAffectedCount,\n                isRegenerating: regenerateModal.isRegenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"VnghEzLs1CqEN9hL+NMXO2UFFPQ=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\n"));

/***/ })

});