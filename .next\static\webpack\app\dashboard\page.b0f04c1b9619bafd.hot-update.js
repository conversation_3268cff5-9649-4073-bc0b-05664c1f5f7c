"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/ChatInterface.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\");\n/* harmony import */ var _AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AttachmentDisplay */ \"(app-pages-browser)/./src/components/dashboard/AttachmentDisplay.tsx\");\n/* harmony import */ var _DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteMessageModal */ \"(app-pages-browser)/./src/components/dashboard/DeleteMessageModal.tsx\");\n/* harmony import */ var _RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RegenerateMessageModal */ \"(app-pages-browser)/./src/components/dashboard/RegenerateMessageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Componente otimizado para mensagem individual\nconst MessageBubble = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = (param)=>{\n    let { message, isAnimated, onDelete, onRegenerate, onEdit, onCopy, editingMessageId, editingContent, setEditingContent, handleEditSave, handleEditCancel } = param;\n    const formatTime = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString(\"pt-BR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const MessageActions = (param)=>/*#__PURE__*/ {\n        let { message, isUser } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onDelete(message.id),\n                    className: \"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Excluir mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCopy(message.content),\n                    className: \"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Copiar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onEdit(message.id, message.content),\n                    className: \"p-1.5 text-white/40 hover:text-yellow-400 hover:bg-yellow-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Editar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined),\n                !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onRegenerate(message.id),\n                    className: \"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Regenerar resposta\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n            lineNumber: 73,\n            columnNumber: 5\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-start space-x-2 group \".concat(isAnimated ? \"animate-message-slide-in\" : \"\", \" \").concat(message.sender === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 \".concat(message.sender === \"user\" ? \"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30\" : \"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-white text-sm font-bold drop-shadow-sm\",\n                    children: message.sender === \"user\" ? \"U\" : \"AI\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"backdrop-blur-sm rounded-2xl p-3 max-w-3xl border \".concat(message.sender === \"user\" ? \"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto\" : \"bg-blue-600/20 border-blue-500/20 rounded-tl-md\"),\n                        children: editingMessageId === message.id ? // Campo de edição inline\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: editingContent,\n                                    onChange: (e)=>setEditingContent(e.target.value),\n                                    className: \"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                                    placeholder: \"Digite sua mensagem...\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-end space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleEditCancel,\n                                            className: \"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-lg transition-colors\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleEditSave,\n                                            className: \"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors\",\n                                            children: \"Salvar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                message.attachments && message.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        attachments: message.attachments\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 17\n                                }, undefined),\n                                message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    content: message.content,\n                                    hasWebSearch: message.hasWebSearch,\n                                    webSearchAnnotations: message.webSearchAnnotations\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mt-2 \".concat(message.sender === \"user\" ? \"justify-end\" : \"justify-start\"),\n                        children: message.sender === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                    message: message,\n                                    isUser: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/40 text-xs\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/40 text-xs\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                    message: message,\n                                    isUser: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n}, (prevProps, nextProps)=>{\n    // Comparação customizada para evitar re-renderização desnecessária\n    return prevProps.message.id === nextProps.message.id && prevProps.message.content === nextProps.message.content && prevProps.message.timestamp === nextProps.message.timestamp && prevProps.isAnimated === nextProps.isAnimated && prevProps.editingMessageId === nextProps.editingMessageId && prevProps.editingContent === nextProps.editingContent;\n});\n_c1 = MessageBubble;\nfunction ChatInterface(param) {\n    let { messages, isLoading, isLoadingChat, onDeleteMessage, onRegenerateMessage, onEditMessage, onCopyMessage } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [animatedMessages, setAnimatedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [deleteModal, setDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        isDeleting: false\n    });\n    const [regenerateModal, setRegenerateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        messagesAffectedCount: 0,\n        isRegenerating: false\n    });\n    // Controlar animações das mensagens para evitar re-animação durante streaming\n    const prevMessageCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Só animar quando realmente há novas mensagens (não atualizações de conteúdo)\n        if (messages.length > prevMessageCountRef.current) {\n            const newMessages = messages.slice(prevMessageCountRef.current);\n            const newMessageIds = new Set(newMessages.map((msg)=>msg.id));\n            setAnimatedMessages((prev)=>new Set([\n                    ...prev,\n                    ...newMessageIds\n                ]));\n            // Remover animação após completar para evitar acúmulo de memória\n            setTimeout(()=>{\n                setAnimatedMessages((prev)=>{\n                    const updated = new Set(prev);\n                    newMessageIds.forEach((id)=>updated.delete(id));\n                    return updated;\n                });\n            }, 400); // Duração da animação (300ms) + buffer\n        }\n        prevMessageCountRef.current = messages.length;\n    }, [\n        messages.length\n    ]);\n    const scrollToBottom = ()=>{\n        // Usar requestAnimationFrame para otimizar o scroll\n        requestAnimationFrame(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Debounce o scroll para evitar múltiplas chamadas\n        const timeoutId = setTimeout(()=>{\n            scrollToBottom();\n        }, 100);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        messages\n    ]);\n    const formatTime = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString(\"pt-BR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleStartEdit = (message)=>{\n        setEditingMessageId(message.id);\n        setEditingContent(message.content);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingMessageId && editingContent.trim() !== \"\") {\n            onEditMessage(editingMessageId, editingContent.trim());\n        }\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    const handleCancelEdit = ()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    // Callbacks otimizados para evitar re-criação em cada render\n    const handleDeleteCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId)=>{\n        const message = messages.find((msg)=>msg.id === messageId);\n        if (message) {\n            handleDeleteClick(message);\n        }\n    }, [\n        messages\n    ]);\n    const handleEditCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, content)=>{\n        const message = messages.find((msg)=>msg.id === messageId);\n        if (message) {\n            handleStartEdit(message);\n        }\n    }, [\n        messages\n    ]);\n    const handleDeleteClick = (message)=>{\n        setDeleteModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        setDeleteModal((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await onDeleteMessage(deleteModal.messageId);\n            setDeleteModal({\n                isOpen: false,\n                messageId: \"\",\n                messageContent: \"\",\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            setDeleteModal((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        setDeleteModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            isDeleting: false\n        });\n    };\n    const handleRegenerateClick = (message)=>{\n        // Encontrar o índice da mensagem atual\n        const messageIndex = messages.findIndex((msg)=>msg.id === message.id);\n        // Contar quantas mensagens vêm depois desta (apenas as posteriores serão removidas)\n        const messagesAfterCount = messages.length - messageIndex - 1;\n        setRegenerateModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            messagesAffectedCount: messagesAfterCount,\n            isRegenerating: false\n        });\n    };\n    const handleRegenerateConfirm = async ()=>{\n        // Fechar o modal imediatamente\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n        try {\n            // Iniciar a regeneração (não aguardar conclusão)\n            onRegenerateMessage(regenerateModal.messageId);\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n        }\n    };\n    const handleRegenerateCancel = ()=>{\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n    };\n    const MessageActions = (param)=>/*#__PURE__*/ {\n        let { message, isUser } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleDeleteClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Excluir mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 7\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleRegenerateClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Regenerar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleStartEdit(message),\n                    className: \"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Editar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCopyMessage(message.content),\n                    className: \"p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Copiar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n            lineNumber: 430,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n        style: {\n            maxHeight: \"100%\"\n        },\n        children: [\n            isLoadingChat ? // Estado de carregamento de chat\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Carregando mensagens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Aguarde enquanto carregamos o hist\\xf3rico da conversa...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this) : messages.length === 0 ? // Área vazia quando não há mensagens - centralizada e mais bonita\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Comece uma nova conversa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Digite sua mensagem abaixo para come\\xe7ar a conversar com a IA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 grid grid-cols-1 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-blue-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Fa\\xe7a perguntas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Tire d\\xfavidas sobre qualquer assunto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-cyan-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Pe\\xe7a ajuda com c\\xf3digo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Programa\\xe7\\xe3o, debugging e explica\\xe7\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-purple-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Crie conte\\xfado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Textos, resumos e ideias criativas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 520,\n                columnNumber: 9\n            }, this) : // Container das mensagens com altura controlada\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 min-h-0\",\n                children: [\n                    // Mensagens do chat\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-2 group \".concat(animatedMessages.has(message.id) ? \"animate-message-slide-in\" : \"\", \" \").concat(message.sender === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 \".concat(message.sender === \"user\" ? \"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30\" : \"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                        children: message.sender === \"user\" ? \"U\" : \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm rounded-2xl p-3 max-w-3xl border \".concat(message.sender === \"user\" ? \"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto\" : \"bg-blue-600/20 border-blue-500/20 rounded-tl-md\"),\n                                            children: editingMessageId === message.id ? // Campo de edição inline\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: editingContent,\n                                                        onChange: (e)=>setEditingContent(e.target.value),\n                                                        className: \"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                                                        placeholder: \"Digite sua mensagem...\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleCancelEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200\",\n                                                                children: \"Cancelar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSaveEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200\",\n                                                                children: \"Salvar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    message.attachments && message.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        attachments: message.attachments,\n                                                        isUserMessage: message.sender === \"user\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        content: message.content,\n                                                        hasWebSearch: message.hasWebSearch,\n                                                        webSearchAnnotations: message.webSearchAnnotations\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mt-2 \".concat(message.sender === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                            children: message.sender === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-2 animate-message-slide-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                    children: \"AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.4s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm animate-pulse\",\n                                                children: \"Digitando...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef,\n                        className: \"h-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 589,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: deleteModal.isOpen,\n                onClose: handleDeleteCancel,\n                onConfirm: handleDeleteConfirm,\n                messagePreview: deleteModal.messageContent,\n                isDeleting: deleteModal.isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: regenerateModal.isOpen,\n                onClose: handleRegenerateCancel,\n                onConfirm: handleRegenerateConfirm,\n                messagePreview: regenerateModal.messageContent,\n                messagesAffectedCount: regenerateModal.messagesAffectedCount,\n                isRegenerating: regenerateModal.isRegenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n        lineNumber: 486,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"oYRxAcOc12VUsMtESmHzI3Pm+Ao=\");\n_c2 = ChatInterface;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MessageBubble$memo\");\n$RefreshReg$(_c1, \"MessageBubble\");\n$RefreshReg$(_c2, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\n"));

/***/ })

});